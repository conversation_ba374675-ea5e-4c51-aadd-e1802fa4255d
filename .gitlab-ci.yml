image: ubuntu

before_script:
  # 修改 ~/.npmrc 文件

  - echo '@meitu:registry=http://npm.meitu-int.com' > ~/.npmrc
  - echo "registry=https://registry.npmmirror.com" >> ~/.npmrc

stages:
  - build
  - deploy
  - notify

node-build-pre:
  stage: build
  image: swr.cn-north-4.myhuaweicloud.com/opsci/whee-global-node-v22-11-0:v1
  only:
    - /^pre-\d+.\d+.\d+$/
  artifacts:
    paths:
      - ./
  except:
    - master
  script:
    - cat  ~/.npmrc
    - yarn install
    - yarn build:pre
    - ls -la .next/static/chunks
    - rm -rf .next/cache
    - ls -a
    # - ossutil config -e ${OBS_ENDPOINT} -i ${OBS_AK} -k ${OBS_SK}
    # build 推送build目录
    # - ossutil sync .next/static oss://wheeai-pre.stariidata.com/wheeai-pre/static/_next/ --disable-all-symlink --delete -u -f
    # - obsutil config -i=${OBS_AK} -k=${OBS_SK} -e=${OBS_ENDPOINT}
    # - obsutil sync .next/static obs://hellorf-static-test/pre/plus/_next/static
    - /bin/obs-push -s .next/static -ep obs.cn-north-4.myhuaweicloud.com -d ${STATIC_DEPOLY_PATH_NEXT_PRE} -ak ${ACCESS_KEY_ID} -sk ${SECRET_ACCESS_KEY} -bucket whee-static-fe

deploy-image-pre:
  stage: deploy
  # image: zcool-registry-vpc.cn-beijing.cr.aliyuncs.com/public/opsci/zcool_node:v16.15.1-5
  # image: swr.cn-north-4.myhuaweicloud.com/gitlab-ci/publisher:latest
  image: publisher:latest
  only:
    # - pre
    - /^pre-\d+.\d+.\d+$/
  script:
    - echo "PROJECT = whee-web" > ./matrix.conf
    - echo "NAMESPACE = whee-web-fe" >> ./matrix.conf
    - echo "ENV = pre" >> ./matrix.conf
    - publish.sh pre

node-build-beta:
  stage: build
  image: swr.cn-north-4.myhuaweicloud.com/opsci/whee-global-node-v22-11-0:v1
  only:
    - /^beta-\d+.\d+.\d+$/
  artifacts:
    paths:
      - ./
  except:
    - master
  script:
    - cat  ~/.npmrc
    - yarn install
    - yarn build:beta
    - ls -la .next/static/chunks
    - rm -rf .next/cache
    - ls -a
    - /bin/obs-push -s .next/static -ep obs.cn-north-4.myhuaweicloud.com -d ${STATIC_DEPOLY_PATH_NEXT_BETA} -ak ${ACCESS_KEY_ID} -sk ${SECRET_ACCESS_KEY} -bucket whee-static-fe

deploy-image-beta:
  stage: deploy
  image: publisher:latest
  only:
    # - beta
    - /^beta-\d+.\d+.\d+$/
  script:
    - echo "PROJECT = whee-web" > ./matrix.conf
    - echo "NAMESPACE = whee-web-fe" >> ./matrix.conf
    - echo "ENV = beta" >> ./matrix.conf
    - publish.sh beta

node-build-release:
  stage: build
  image: swr.cn-north-4.myhuaweicloud.com/opsci/whee-global-node-v22-11-0:v1
  only:
    - /^release-\d+.\d+.\d+$/
  artifacts:
    paths:
      - ./
  except:
    - master
  script:
    - cat  ~/.npmrc
    - yarn install
    - yarn build:release
    - ls -la .next/static/chunks
    - rm -rf .next/cache
    - ls -a
    - /bin/ossutil -f -u -e ${OSS_ENDPOINT} -i ${OSS_AK_RELEASE} -k ${OSS_SK_RELEASE} cp -r .next/static oss://wheeai-release/wheeai-release/_next/static

deploy-image-release:
  stage: deploy
  image: publisher:latest
  only:
    # - release
    - /^release-\d+.\d+.\d+$/
  script:
    - echo "PROJECT = whee-web" > ./matrix.conf
    - echo "NAMESPACE = whee-web-fe" >> ./matrix.conf
    - echo "ENV = release" >> ./matrix.conf
    - publish.sh release starii
