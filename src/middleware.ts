import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { handleApiProxy, authMiddleware } from "./services/middlewares";
import { createI18nMiddleware } from "next-international/middleware";
import { Locales } from "./constants/locale";

const I18nMiddleware = createI18nMiddleware({
  locales: Locales,
  defaultLocale: "zh",
  urlMappingStrategy: "rewrite",
});

/**
 * 主中间件函数，整合所有中间件逻辑
 */
export async function middleware(request: NextRequest) {
  // API 代理中间件
  const apiProxyResponse = handleApiProxy(request);
  if (apiProxyResponse) return apiProxyResponse;

  // const authResponse = await authMiddleware(request);
  // if (authResponse) return authResponse;

  return I18nMiddleware(request);
}

// 配置匹配的路径
export const config = {
  matcher: [
    // API 代理的匹配路径
    "/aigc/api/:path*",

    // 国际化
    "/((?!api|static|.*\\..*|_next|favicon.ico|robots.txt).*)",
  ],
};
