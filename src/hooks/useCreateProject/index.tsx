import {
  SaveProjectRequest,
  SaveProjectResponse,
} from "@/api/types/aiPoster/project";
// import { SubscribeModal } from "@/components/SubscribeModal";

import { SubscribeModalType } from "@/components/SubscribeModal/types";
import {
  MEMBER_EXPIRED_CODE,
  MEMBER_EXPIRED_CODE_PLUS,
} from "@/constants/errorCode";
import { GlobalProjectStore } from "@/stores/GlobalProjectStore";
import { UserStore } from "@/stores/UserStore";
import { wheeLoginPopup } from "@/utils/account";
import { useI18n } from "@/locales/client";

type UseCreateProjectDeps = {
  userStore: UserStore;
  globalProjectStore: GlobalProjectStore;
};

export function useCreateProject({
  userStore,
  globalProjectStore,
}: UseCreateProjectDeps) {
  const t = useI18n();

  // const { open } = useSubscribeModal();

  // const openSubscribeModal = (info?: Partial<Omit<SaveProjectRequest, 'projectId'>>) => {
  //   return new Promise<SaveProjectResponse>((resolve, reject) => {
  //     open({
  //       productType: SubscribeModalType.Project,
  //       onSuccess() {
  //         globalProjectStore.createProject({
  //           projectName: t('Unnamed'),
  //           ...info,
  //         })
  //           .then(res => {
  //             resolve(res);
  //           })
  //           .catch(reject);
  //       },

  //       onClose() {
  //         console.log('close');
  //         reject();
  //       }
  //     });
  //   });
  // }

  const createProject = async (
    info?: Partial<Omit<SaveProjectRequest, "projectId">>
  ) => {
    if (!userStore.isLogin) {
      await wheeLoginPopup({
        useLoginMethod: "third-party",
      });
    }

    if (!userStore.isLogin) {
      return;
    }

    // if (!globalProjectStore.canCreateProject) {
    //   return await openSubscribeModal();
    // }

    try {
      return await globalProjectStore.createProject({
        projectName: t("Unnamed"),
        ...info,
      });
    } catch (e: any) {
      // if (e?.response?.data?.code === MEMBER_EXPIRED_CODE || e?.response?.data?.code === MEMBER_EXPIRED_CODE_PLUS) {
      //   return await openSubscribeModal();
      // }

      throw e;
    }
  };

  return {
    createProject,
  };
}
