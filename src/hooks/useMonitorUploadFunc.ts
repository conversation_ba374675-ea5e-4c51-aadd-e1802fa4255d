import { useStore } from "@/contexts/StoreContext";
import { createUploaderProcessor } from "@/utils/uploader";
import { beforeValidatorPass } from "@/utils/uploader/middleware/beforeValidatorPass";
import { BeforeValidatorOptions } from "@/utils/uploader/middleware/beforeValidatorPass/types";
import { monitorPass } from "@/utils/uploader/middleware/monitorPass";
import { uploadPass } from "@/utils/uploader/middleware/uploadPass";
import { useMemo } from "react";

type UseMonitorUploadFuncOptions = {
  beforeValidator?: Partial<BeforeValidatorOptions>
}
export function useMonitorUploadFunc(options?: UseMonitorUploadFuncOptions) {

  const beforeValidatorOptions = Object.assign({
    extension: ['png', 'jpg'],
    size: 30,
    dimension: {
      min: 50,
      max: 9500,
    },
  }, options?.beforeValidator);

  const { userStore } = useStore();
  const accessToken = userStore.authToken;

  const upload = useMemo(() => {
    if (!accessToken) {
      return;
    }
    return createUploaderProcessor([
      beforeValidatorPass(beforeValidatorOptions),
      uploadPass({ accessToken }),
      monitorPass(),
    ])
  }, [accessToken]);

  return upload;
}