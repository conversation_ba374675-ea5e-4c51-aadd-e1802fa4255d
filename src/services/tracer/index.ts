// 统计SDK
import type { InitOptions, CommonStatsParams } from "@meitu/mtstat-sdk";
import { isDebugMode } from "@meitu/util";

import { extractUTMParams, TracerUTM } from "@meitu/tracer-utm-params";

import { isBeta, isRelease } from "@/constants/environment";

import { getConfig } from "@/config";
const config = getConfig();
// export const tracer = mtTracer;

import { setMtstatReady } from "@/utils/mtstatReady";
import { getTraceChannelParams } from "./channel";

export const initTracer = async () => {
  // 确保只在客户端（浏览器环境）执行
  if (typeof window === "undefined") {
    return;
  }
  const mtTracer = await import("@meitu/mtstat-sdk");
  const tracer = mtTracer.default;
  window.mtstat = tracer;

  try {
    let initConfig: InitOptions = {
      moduleId: config.STATISTIC_MODULE_ID,
      appVersion: "1.0.0",
      appKey: config.STATISTIC_APP_KEY,
      appLanguage: document.documentElement.lang || navigator.language,
      env: isRelease ? "release" : "test",
      logger: isDebugMode("debug:tracer"),
    };

    let commonStatsParams: CommonStatsParams | undefined = undefined;

    // const [
    //   commonParams,
    //   wheeInitConfig
    // ] = getTraceChannelParams();

    // Object.assign(initConfig, wheeInitConfig);

    const [commonParams] = getTraceChannelParams();

    Object.assign(initConfig);

    const utm: any = extractUTMParams();
    // console.log('utm=>>>>>>>>>', utm);
    let _commonStatsParams = {
      ...utm?.statsParams,
      ...(commonStatsParams || {}),
      channel: utm?.channel || "",
    };
    initConfig.channel = utm?.channel || "";

    tracer.init(initConfig);
    tracer.setCommonStatsParams({
      ..._commonStatsParams,
      ...commonParams,
    });
    setMtstatReady();
  } catch (error) {
    if (process.env.NEXT_PUBLIC_ENV === "dev") {
      console.error("Failed to initialize tracer:", error);
    }
  }
};

export const trackEvent = (event: string, params?: any) => {
  // 确保只在客户端（浏览器环境）执行
  if (typeof window === "undefined") {
    return;
  }
  try {
    // console.log('trackEvent=>>>>>>>>>', event, params);
    // console.log(window.mtstat);
    window.mtstat.track(event, params);
  } catch (error) {
    if (process.env.NEXT_PUBLIC_ENV === "dev") {
      console.error("Tracer error:", error);
    }
  }
};

/**
 * 埋点登陆
 * @param uid
 */
export function trackAccountId(uid: string | number) {
  if (typeof window === "undefined") {
    return;
  }
  try {
    window.mtstat.login(uid);
  } catch (error) {
    if (process.env.NEXT_PUBLIC_ENV === "dev") {
      console.error("Tracer error:", error);
    }
  }
}

/**
 * 追踪用户
 */
export const followUser = (uid: string | number) => {
  if (typeof window === "undefined") {
    return;
  }
  try {
    window.mtstat.login(uid);
  } catch (error) {
    if (process.env.NEXT_PUBLIC_ENV === "dev") {
      console.error("Tracer error:", error);
    }
  }
};

/**
 * 取消追踪用户
 */
export const unfollowUser = () => {
  if (typeof window === "undefined") {
    return;
  }
  try {
    window.mtstat.logout();
  } catch (error) {
    if (process.env.NEXT_PUBLIC_ENV === "dev") {
      console.error("Tracer error:", error);
    }
  }
};
