import classNames from 'classnames';
import styles from './index.module.scss';
import { ChevronLeftNarrowBlack } from '@meitu/candy-icons';
import { useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { useRootStore } from '../../_store';

export type DrawerProps = React.PropsWithChildren<{
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  disabled?: boolean;
}>;

function Drawer({ children, open = true, onOpenChange, disabled }: DrawerProps) {
  
  const { editorStatusStore } = useRootStore();

  const elementRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    editorStatusStore.templatePanelContainer = elementRef.current;
  });
  
  const handleClickTrigger = () => {
    onOpenChange?.(!open);
  };

  return (
    <div className={classNames(styles.drawer, { open, disabled })} ref={elementRef}>
      <div className="drawer-trigger" onClick={handleClickTrigger}>
        <ChevronLeftNarrowBlack className="drawer-trigger-icon" />
      </div>
      <div className="drawer-content">{children}</div>
    </div>
  );
}

export default observer(Drawer);
