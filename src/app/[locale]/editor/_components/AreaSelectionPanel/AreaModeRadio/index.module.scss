@import "@/styles/variable.scss";

.radio {
  user-select: none;
  height: 32px;
  padding: 4px;
  border-radius: 12px;
  border: 1px solid $system-stroke-input-default;
  background: $system-background-input;
  color: $system-content-secondary;
  display: flex;
  align-items: center;
  justify-content: center;


  :global {
    .radio-btn {
      padding: 6px 8px;
      min-width: 70px;
      height: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      cursor: pointer;

      &-input {
        display: none;
      }

      &-icon {
        svg {
          width: 18px;
          height: 18px;
        }
      }

      &.radio-btn-active {
        color: #fff;
        border-radius: 10px;
        // border: 1px solid $system-stroke-button;
        background: $system-background-thirdary;
        box-shadow:
          0px 0px 2px 0px rgba(0, 0, 0, 0.08),
          0px 6px 24px 0px rgba(0, 0, 0, 0.06),
          inset 0 0 0 1px $system-stroke-button;
      }
    }
  }
}