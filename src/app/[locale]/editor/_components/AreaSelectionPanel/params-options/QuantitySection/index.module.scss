.genNum:global(.ant-radio-group) {
  width: 100%;
  display: flex;
  justify-content: space-between;

  :global {
    .gen-num-radio.ant-radio-wrapper {
      margin: 0;

      .ant-wave-target {
        display: none;

        &+span {
          padding: 0;
        }
      }

      .gen-num-radio-label {
        display: flex;
        width: 69px;
        height: 32px;
        justify-content: center;
        align-items: center;
        border-radius: var(--radius-8, 8px);
        border: 1px solid #E2E8F0;
        background: #fff;
      }

      &.ant-radio-wrapper-checked {
        .gen-num-radio-label {
          box-shadow: 0 0 0 1.5px #3549FF inset;
          background: rgba(53, 73, 255, 0.05);
          color: #293545;
        }
      }
    }
  }
}