
import styles from "./index.module.scss";
import classNames from "classnames";
import { observer } from "mobx-react-lite";
import { useEffect } from "react";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { Slider } from "@/components";

type PaintSizeProps = {
  value?: number;
  onChange?(size: number): void;
  min?: number;
  max?: number;
  className?: string;
};

function PaintSize({
  value = 50,
  onChange,
  className,
  min = 1,
  max = 100,
}: PaintSizeProps) {
  const { renderStore } = useRootStore();

  useEffect(() => {
    const hotkey = renderStore.renderHotkey;
    if (!hotkey) {
      return;
    }

    const clamped = (value: number) => {
      return Math.min(max, Math.max(min, value));
    };

    const increaseBrushWidthHotkey = () => {
      onChange?.(clamped(value + 1));
    };

    const decreaseBrushWidthHotKey = () => {
      onChange?.(clamped(value - 1));
    };

    hotkey.registerHotKey("=", increaseBrushWidthHotkey);
    hotkey.registerHotKey("-", decreaseBrushWidthHotKey);

    return () => {
      hotkey.unregisterHotKey("=", increaseBrushWidthHotkey);
      hotkey.unregisterHotKey("-", decreaseBrushWidthHotKey);
    };
  }, [renderStore.renderHotkey, min, max, value, onChange]);

  return (
    <div className={classNames(styles.size, className)}>
      <div className="size-label">Size</div>
      <div className="size-input-container">
        <span className="size-input-range min">{min}</span>
        <Slider
          value={value}
          onChange={onChange}
          markNum={0}
          min={min}
          max={max}
        />
        <span className="size-input-range max">{max}</span>
      </div>
    </div>
  );
}

export default observer(PaintSize);
