@import '@/styles/variable.scss';

.button {
  min-width: 118px;
  padding: 0 10px;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: var(--radius-8, 8px);
  background: $system-content-brand-primary;
  color: #fff;
  font-size: 14px;
  user-select: none;

  &:disabled {
    color: $system-content-secondary;
    background: linear-gradient(0deg, var(--system-content-disabled, #868A94) 0%, var(--system-content-disabled, #868A94) 100%), var(--system-content-brandPrimary, #3549FF);
    cursor: not-allowed;
  }

  &:hover {
    opacity: 0.8;
  }

  :global {
    .mt-bean-icon {
      margin-right: 6px;

      svg {
        margin-right: 2px;
        color: #FBCD6F;
      }
    }
  }
}