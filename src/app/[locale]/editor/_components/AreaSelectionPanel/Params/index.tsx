import classNames from 'classnames';
import styles from './index.module.scss'
import { DoubleAdjustBold } from '@meitu/candy-icons';
import { Form, FormInstance, Popover } from 'antd';
import { useRef } from 'react';
import { FormProps } from 'antd/lib';

type ParamsProps = {
  prompt?: string;
  onPromptChange?: (value: string) => void;
  className?: string;
  placeholder?: string;
  onPromptInputKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;

  formProps?: FormProps;
  renderFormItems?: () => React.ReactNode;
  defaultFormValues?: Record<string, any>;
}

export function Params({
  prompt,
  onPromptChange,
  placeholder,
  onPromptInputKeyDown,
  formProps,
  renderFormItems,
}: ParamsProps) {

  const containerRef = useRef<HTMLDivElement>(null);

  return (
    <div className={styles.params} ref={containerRef}>
      <input
        className="prompt-input"
        value={prompt}
        placeholder={placeholder}
        onChange={(e) => {
          onPromptChange?.(e.target.value);
        }}
        onKeyDown={onPromptInputKeyDown}
      />

      <Popover
        trigger={"click"}
        classNames={{
          body: styles.options,
        }}
        content={!!renderFormItems && (
          <Form {...formProps}>
            {renderFormItems()}
          </Form>
        )}
        getPopupContainer={() => containerRef.current!}
        forceRender
      >
        <button className={classNames("params-button")}>
          <DoubleAdjustBold/>
        </button>
      </Popover>
    </div>
  )
}