.editor-image-bar {
  position: fixed;
  bottom: 16px;
  // 水平居中
  left: 50%;
  transform: translateX(-50%);
  border-radius: var(--radius-16, 16px);
  border: 1px solid rgba(227, 228, 232, 0.5);
  background: rgba(255, 255, 255, 0.70);
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 8px 24px 0px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(16px);
  display: flex;
  flex-direction: row;
  height: 88px;
  padding-left: 12px;

  .image-bar-item {
    width: 72px;
    height: 72px;
    // padding-left: 10px;
    margin-top: -8px;
    cursor: pointer;
    border: 2px solid #fff;
    background-color: #fff;
    border-radius: 12px;
    box-sizing: border-box;

    .ant-image {
      width: 68px;
      height: 68px;

      img {
        object-fit: cover;
        width: 68px;
        height: 68px;
        border-radius: 12px;
        background-color: #fff;
        user-select: none;
        -webkit-user-drag: none;
        // background: url(<path-to-image>) lightgray 0px -0.288px / 100% 100.446% no-repeat, url(<path-to-image>) lightgray 50% / cover no-repeat, linear-gradient(0deg, rgba(255, 255, 255, 0.00) 0%, rgba(255, 255, 255, 0.00) 100%), url(<path-to-image>) lightgray 50% / cover no-repeat, url(<path-to-image>) lightgray 50% / cover no-repeat, url(<path-to-image>) lightgray 50% / cover no-repeat;

        /* level_3 */
        box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
          0px 10px 40px 0px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .active {
    width: 78px;
    height: 78px;
    // 上移 10px
    margin-top: -16px;

    .ant-image {
      width: 74px;
      height: 74px;

      img {
        width: 74px;
        height: 74px;
        overflow: hidden;
        // border: 2px solid #1890ff;
      }
    }
  }

  .image-bar-item1 {
    transform: rotate(-3.758deg);
    z-index: 10;
    position: relative;

    .origin-tag {
      position: absolute;
      transform: rotate(-3.758deg);
      top: 1px;
      // left: 12px;
      left: 0px;
      display: flex;
      width: 30px;
      padding: 0px 5px 0px 6px;
      justify-content: center;
      align-items: center;
      border-radius: var(--radius-0, 0px) var(--radius-0, 0px) var(--radius-4, 4px) var(--radius-0, 0px);
      background: rgba(0, 0, 0, 0.6);
      z-index: 8;
      color: var(--content-hoverTips, #fff);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 9px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      border-top-left-radius: 12px;
      border-bottom-right-radius: 4px;
    }
  }

  .image-bar-item2 {
    z-index: 9;
    margin-left: -12px;
  }

  .image-bar-item3 {
    z-index: 8;
    margin-left: -12px;
  }

  .image-bar-item4 {
    z-index: 7;
    margin-left: -12px;
  }

  .image-bar-item5 {
    z-index: 6;
    margin-left: -12px;
  }

  // .image-bar-item奇数偏移 -4deg 偶数便宜 4deg
  .image-bar-item:nth-child(odd) {
    transform: rotate(-4deg);
  }

  .image-bar-item:nth-child(even) {
    transform: rotate(4deg);
  }
}

.poster-editor-image-loading {
  scale: 0.6;
  // margin-top: 20px;
}

.image-bar-item-error {
  background: rgba(0, 0, 0, 0.25);

  backdrop-filter: blur(40px);
  width: 100%;
  height: 100%;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &-text {
    color: var(--_base-white_opacity_100, #fff);
    text-align: center;
    /* text_14 */
    width: 60px;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    transform: scale(0.5);
  }
}