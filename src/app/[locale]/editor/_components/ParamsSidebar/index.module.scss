@import "@/styles/variable.scss";
@import "../zIndex.scss";

$bottom-button-height: 120px;

.sidebar {
  position: absolute;
  left: 0;
  top: 0;
  z-index: $z-index-params-drawer;
  width: 320px;
  height: 100%;
  background: $system-background-secondary;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }

  :global {

    .sidebar-form {
      padding-bottom: $bottom-button-height;
    }

    .sidebar-section {
      box-sizing: border-box;
      width: 320px;
      padding: 16px;
      background: $background-input;
      margin-bottom: 6px;
      color: $system-content-secondary;

      &-title {
        font-size: 14px;
        height: 32px;
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }
    }

    .meidou-button {
      position: fixed;
      width: 320px;
      left: 0;
      bottom: 0;
    }
  }
}