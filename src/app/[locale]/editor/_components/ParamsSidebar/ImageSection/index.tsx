import { observer } from "mobx-react";
import styles from "./index.module.scss";
import { Form, Spin, Switch, Image } from "antd";
import { ChevronRightBlack, PlusBold, TrashCanBold } from "@meitu/candy-icons";
import { useI18n } from "@/locales/client";
import FilePicker from "@/components/FilePicker";
import { useUpload } from "@/app/[locale]/editor/_hooks/useUpload";
import { useState } from "react";
import { toAtlasImageView2URL } from "@meitu/util";
import { trackEvent } from "@/services/tracer";
import CardLoading from "@/components/Loading/card-loading";
import classNames from "classnames";
import { useMonitorUploadFunc } from "@/hooks/useMonitorUploadFunc";
import { useParamsSidebarContext } from "../context";
import { useRootStore } from "../../../_store";


type ImageSectionProps = {
  onImageEditSwitchChange?: (open: boolean) => void;
};

const ImageSection = ({
  onImageEditSwitchChange
}: ImageSectionProps) => {
  const form = Form.useFormInstance();
  const t = useI18n();
  const { selectionStore } = useRootStore();
  const { validateImageEdit } = useParamsSidebarContext();
  
  const singleImage = selectionStore.singleImage;

  return (
    <section className={classNames(styles.image, "sidebar-section")}>
      <div className={classNames("sidebar-section-title", "image-title")}>
        <div className="image-title-content">
          <strong>
            {t("Image Variation")}
          </strong>
        </div>
        <Form.Item name={"imageChecked"} noStyle>
          <Switch className="image-title-switch" onChange={(open, e) => {
            // 如果选中了图片，验证通过才可以打开
            if (open && singleImage && !validateImageEdit(singleImage.image)) {
              e.preventDefault();
              form.setFieldValue('imageChecked', false);
              return;
            }

            onImageEditSwitchChange?.(open)
          }}/>
        </Form.Item>
      </div>

      <Form.Item noStyle dependencies={["imageChecked", "image"]}>
        {() => {
          return  form.getFieldsValue().imageChecked && (
            <Form.Item name={"image"} noStyle>
              <PosterUploader />
            </Form.Item>
          );
        }}
      </Form.Item>
    </section>
  );
};

const PosterUploader = (props: {
  value?: string;
  onChange?: (value: string) => void;
}) => {

  const { value, onChange } = props;
  const t = useI18n();
  const uploadFunc = useMonitorUploadFunc({
    beforeValidator: {
      ratio: {
        wh: 3,
        hw: 1/3,
      },
    }
  })
  const upload = useUpload({ upload: uploadFunc });
  const [uploadLoading, setUploadLoading] = useState(false);

  const handleFileChange = async (fileList: FileList | null) => {
    const file = fileList?.[0];
    if (!file) {
      return;
    }
    setUploadLoading(true);
    const shape = await upload(file);
    setUploadLoading(false);

    trackEvent("upload_image_success", {
      location: "edit_component",
    });
  };

  return (
    <>
      {value ? (
        <div className={styles.imageBox}>
          <div className={styles.leftBox}>
            <Image
              alt=""
              src={toAtlasImageView2URL(value, {
                mode: 2,
                width: 120,
              })}
              preview={false}
              placeholder={<CardLoading />}
            />
          </div>
          <TrashCanBold
            className={styles.rightIcon}
            onClick={() => {
              onChange?.("");
            }}
          />
        </div>
      ) : (
        <FilePicker
          accept={[".png", ".jpg", ".jpeg"]}
          onChange={handleFileChange}
        >
          {({ openPicker }) => {
            return (
              <Spin spinning={uploadLoading}>
                <div
                  className={styles.imageBox}
                  onClick={() => {
                    trackEvent("upload_image_click", {
                      location: "edit_component",
                    });
                    openPicker();
                  }}
                >
                  <div className={styles.leftBox}>
                    <PlusBold />
                  </div>
                  <div className={styles.centerBox}>{t("Upload")}</div>
                  <ChevronRightBlack className={styles.rightIcon} />
                </div>
              </Spin>
            );
          }}
        </FilePicker>
      )}
    </>
  );
};

export default observer(ImageSection);
