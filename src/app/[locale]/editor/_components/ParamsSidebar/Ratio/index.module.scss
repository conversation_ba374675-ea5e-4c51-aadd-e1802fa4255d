@import "@/styles/variable.scss";

.ratio {
  :global {
    .ratio-selection.ant-select {
      width: 100%;

      .ant-select-selector {
        box-shadow: none !important;
      }

      &:hover {
        .ant-select-selector {
          border-color: $system-content-brand-primary !important;
        }
      }

      &.ant-select-open {
        .ant-select-selector {
          border-color: $system-content-brand-primary !important;
        }
      }
    }
  }
}

.label {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;

  &:global(.disabled) {
    cursor: not-allowed;
    opacity: 0.5;
  }

  :global {
    .label-icon {
      display: flex;
      align-items: center;

      svg {
        width: 16px;
        height: 16px;
        color: $system-content-secondary;
      }
    }
  
    .label-text {
      color: $system-content-secondary;
      font-size: 12px;
      margin-left: 6px;
    }
  }
}