import { BaseModeSizeRatio } from "@/api/types/editorConfig";
import { ChevronDownBold, Picture, Ratio11, Ratio169, <PERSON>io23, <PERSON>io32, <PERSON>io34, <PERSON>io43, Ratio916 } from "@meitu/candy-icons";
import { Select } from "antd";
import classNames from "classnames";
import { observer } from "mobx-react-lite";
import { useMemo } from "react";
import styles from './index.module.scss';

const iconMap: Record<string, React.ReactNode> = {
  'self-adaption': <Picture/>,
  '1:1': <Ratio11/>,
  '2:3': <Ratio23/>,
  '3:2': <Ratio32/>,
  '3:4': <Ratio34/>,
  '4:3': <Ratio43/>,
  '9:16': <Ratio916/>,
  '16:9': <Ratio169/>,
  '21:9': <Ratio169/>,
  '9:21': <Ratio916/>,
}

const allRatio = [
  'self-adaption',
  '1:1',
  '3:2',
  '3:4',
  '4:3',
  '9:16',
  '16:9',
  '21:9',
  '9:21',
];

type RatioProps = {
  options?: BaseModeSizeRatio[];
  value?: string;
  onChange?: (value: string) => void;
  imageEdit?: boolean;
}

function Ratio({
  options,
  value,
  onChange,
  imageEdit,
}: RatioProps) {
  
  const items = useMemo(() => {
    const optionsMap = new Map(options?.map(item => [item.ratio, item]));

    // 图生图不需要展示自适应
    const displayRatio = imageEdit ? allRatio : allRatio.slice(1);

    return displayRatio.map(ratio => {
      const enabled = optionsMap.get(ratio);
      return {
        label: (
          <div className={classNames(styles.label, !enabled && 'disabled')}>
            <span className="label-icon">
              {iconMap[ratio]}
            </span>
            <span className="label-text">
              {ratio === 'self-adaption' ? '自适应' : ratio}
            </span>
          </div>
        ),
        value: ratio,
        disabled: !enabled,
      }
    });
  }, [options, imageEdit])


  return (
    <section className={classNames(styles.ratio, "sidebar-section")}>
      <div className="sidebar-section-title">画面比例</div>
      <Select
        rootClassName="ratio-selection"
        value={value}
        options={items}
        onChange={onChange}
        popupClassName={styles.popup}
        suffixIcon={<ChevronDownBold/>}
      />
    </section>
  )
}

export default observer(Ratio);