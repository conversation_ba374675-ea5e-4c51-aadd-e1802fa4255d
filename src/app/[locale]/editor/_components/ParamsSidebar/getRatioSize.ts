import { EditorConfigBaseModelListResponse } from "@/api/types/editorConfig";

const selfAdaptionRatio = {
  ratio: "self-adaption",
  width: 0,
  height: 0,
};
/**
 * 对于图生图，服务端不会下发自适应尺寸，因此前端自己塞一个！
 * 对于文生图，需要剔除自适应
 */
export const getRatioSize = (
  model: EditorConfigBaseModelListResponse,
  imageEdit: boolean
) => {
  if (imageEdit) {
    const ratios = model.defaultParams.sizeRatio;
    if (ratios.find((r) => r.ratio === "self-adaption")) {
      return ratios;
    }

    return [selfAdaptionRatio, ...ratios];
  } else {
    const ratios = model.defaultParams.sizeRatio;
    if (ratios.find((r) => r.ratio === "self-adaption")) {
      return ratios.filter((r) => r.ratio !== "self-adaption");
    }

    return ratios;
  }
};
