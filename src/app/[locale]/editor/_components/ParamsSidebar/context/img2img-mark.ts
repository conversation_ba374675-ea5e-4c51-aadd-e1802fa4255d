import { ElementName, FabricObject, getElementOptions, ImageOptions, Render } from "@meitu/whee-infinite-canvas";
import { RenderStore } from "../../../_store/render";
import { ProjectsStore } from "../../../_store/projects";
import { saveElements } from "../../../_utils/saveProjectHistory";

/**
 * 图生图参考图逻辑
 * 1. 手动打开（以下均需验证图片是否满足要求）
 *    1.1 点击提示词模版
 *    1.2 点击图生图按钮
 *    1.3 点击场景模版
 * 2. 通过手动打开的图片后，会获得一个标记imageToImageEnable
 * 3. 自动打开逻辑：选中有imageToImageEnable的图片，会自动打开图生图按钮
 * 4. 自动关闭逻辑：取消选中图片，或者选中没有imageToImageEnable的图片，会关闭图生图按钮
 */


function ensureImageShapeOptions(shape: FabricObject, render: Render) {
  const options = getElementOptions.call(render, shape);
  if (options._name_ === ElementName.IMAGE) {
    return options as ImageOptions;
  }
}


function _setImg2ImgFlag(shape: FabricObject, flag: boolean, render: Render) {
  const options = ensureImageShapeOptions(shape, render);
  if (!options) {
    return;
  }

  const customData = options._custom_data_history_;
  shape.set({
    _custom_data_history_: {
      ...customData,
      img2img: flag,
    },
  });
}


export function enableImg2ImgFlag(shape: FabricObject, render: Render) {
  _setImg2ImgFlag(shape, true, render);
}

export function disableImg2ImgFlag(shape: FabricObject, render: Render) {
  _setImg2ImgFlag(shape, false, render);
}

export function getImg2ImgFlag(shape: FabricObject, render: Render) {
  const options = ensureImageShapeOptions(shape, render);
  if (!options) {
    return;
  }

  return options._custom_data_history_?.img2img;
}

type UseImg2ImgFlagDeps = {
  renderStore: RenderStore;
  projectsStore: ProjectsStore,
}
export function useImg2ImgFlag({ renderStore, projectsStore }: UseImg2ImgFlagDeps) {
  const render = renderStore.render;
  const projectId = projectsStore.activeProjectId;

  const enableImg2Img = async (shape: FabricObject) => {
    if (!render || !projectId) {
      return;
    }

    enableImg2ImgFlag(shape, render);
    await saveElements({
      targets: [shape],
      render,
      projectId,
    });
  };

  const disableImg2Img = async (shape: FabricObject) => {
    if (!render || !projectId) {
      return;
    }

    disableImg2ImgFlag(shape, render);
    await saveElements({
      targets: [shape],
      render,
      projectId,
    });
  };

  return {
    enableImg2Img,
    disableImg2Img,
  }
}