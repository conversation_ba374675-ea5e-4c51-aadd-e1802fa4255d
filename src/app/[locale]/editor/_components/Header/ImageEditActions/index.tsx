import { observer } from "mobx-react";
import CutoutAction from "./CutoutAction";
import styles from "./index.module.scss";
import UpscalerAction from "./UpscalerAction";
import TextEditAction from "./TextEditAction";
import EraserAction from "./EraserAction";
import ImageEditAction from "./ImageEditAction";
import SplitAction from "./SplitAction";

function ImageEditActions() {
  return (
    <div className={styles.actions} id="poster-image-actions">
      <CutoutAction />
      <UpscalerAction />
      <EraserAction />
      {/* <ImageEditAction /> */}
      <TextEditAction />
      {/* <SplitAction /> */}
    </div>
  );
}

export default observer(ImageEditActions);
