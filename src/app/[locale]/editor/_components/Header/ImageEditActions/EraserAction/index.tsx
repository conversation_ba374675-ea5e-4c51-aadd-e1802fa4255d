import { Eliminate, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@meitu/candy-icons";
import { Popover, Tooltip } from "antd";
import classNames from "classnames";
import { observer } from "mobx-react";
import styles from "./index.module.scss";
import { useEffect, useMemo, useState } from "react";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { Slider } from "@/components";
import {
  ElementName,
  FabricObject,
  getElementOptions,
  Group,
  IImage,
  LoadingType,
  PathBrush,
  RenderMaskCursor,
  WarningType,
} from "@meitu/whee-infinite-canvas";
import { BrushIcon, EraserIcon } from "@/assets/icons";
import { HeaderAction } from "@/app/[locale]/editor/_store/headerAction";
import { createPureUpload } from "@/utils/uploader";
import { submitTask } from "@/api/aiPoster/task";
import { AIPoster } from "@/api/types/aiPoster/task";
import { init } from "next/dist/compiled/webpack/webpack";
import { dispatch } from "@/app/[locale]/editor/_utils/TaskDispatcher";
import { uploadErrorhandler } from "@/utils/error-handler/upload";
import { handleSubmitError } from "@/app/[locale]/editor/_utils/submit";
import { useI18n } from "@/locales/client";
import { isTextEditing } from "@/app/[locale]/editor/_utils/isTextEditing";

import { useStore } from "@/contexts/StoreContext";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import { fetchPriceDesc } from "@/api/meidou";
import {
  FunctionCode,
  MeiDouFetchPriceDescResponse,
  MeidouQueryBalanceResponse,
} from "@/api/types/meidou";
import { trackEvent } from "@/services/tracer";
import { useMeiDouBalance } from "@/hooks/useMeidou";
import { Track } from "@/app/[locale]/editor/_constant/track";
import { createPortal } from "react-dom";
import { editorLayoutContent } from "@/app/[locale]/editor/_constant/element";
import { useFollowTargets } from "@/app/[locale]/editor/_hooks/useFollowTargets";
import { useEnsureSelectSingleImage } from "@/app/[locale]/editor/_hooks/useEnsureSelectSingleImage";

const EraserContainer = "eraser_mask_container";

function EraserAction() {
  const t = useI18n();
  const rootStore = useRootStore();
  const {
    renderStore,
    projectsStore,
    selectionStore,
    editorStatusStore,
    headerActionStore,
  } = rootStore;

  const image = selectionStore.singleImage?.image;
  const imageOptions = selectionStore.singleImage?.options;

  const { singleImage } = useEnsureSelectSingleImage();

  const { render, renderStyle } = renderStore;
  const [brushSize, setBrushSize] = useState(30);
  const [isEraser, setIsEraser] = useState<boolean>(false);
  const [brush, setBrush] = useState<PathBrush | undefined>(undefined);
  const [price, setPrice] = useState<MeiDouFetchPriceDescResponse | null>(null);

  useEffect(() => {
    if (!image) {
      return;
    }

    let ignore = false;

    // 获取美豆数量
    fetchPriceDesc({
      functionCode: FunctionCode.aiPosterEraser,
      functionBody: JSON.stringify({}),
    }).then((res) => {
      if (ignore) {
        return;
      }
      setPrice(res);
    });

    return () => {
      ignore = true;
    };
  }, [image]);

  const followTargets = useMemo(() => {
    if (singleImage?.image) {
      return [singleImage.image];
    }
  }, [singleImage?.image]);

  const { elementRef, translate } = useFollowTargets<HTMLDivElement>({
    targets: followTargets,
    offset: 16,
    rangePadding: [
      editorStatusStore.canvasVisibleArea.top + 16,
      editorStatusStore.canvasVisibleArea.right + 16,
      editorStatusStore.canvasVisibleArea.bottom + 16,
      editorStatusStore.canvasVisibleArea.left + 16,
    ],
  });

  // 是否显示消除操作栏
  const showEraserOpt =
    headerActionStore.activeHeaderAction === HeaderAction.Eraser;
  const setShowEraserOpt = (val: boolean) => {
    if (val) {
      headerActionStore.setActiveHeaderAction(HeaderAction.Eraser);
    } else {
      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
    }
  };
  const { userStore } = useStore();
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();
  const { availableAmount } = useMeiDouBalance({ userStore });

  const objectLoading = imageOptions?._loading_;
  const imageStatus = imageOptions?._custom_data_history_?.imageStatus;
  const disabled =
    !image || objectLoading || imageStatus === AIPoster.ImageStatus.AuditFailed;

  // 打开消除的快捷键注册
  useEffect(() => {
    const renderHotkey = renderStore.renderHotkey;
    if (!renderHotkey || disabled) return;

    const hotkeyHandler = (event: KeyboardEvent) => {
      if (isTextEditing(renderStore.render)) {
        return;
      }
      event.preventDefault();
      headerActionStore.setActiveHeaderAction(HeaderAction.Eraser);
    };

    renderHotkey.registerHotKey("shift + a", hotkeyHandler);

    return () => {
      renderHotkey.unregisterHotKey("shift + a", hotkeyHandler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [renderStore.renderHotkey, disabled]);

  // 切换涂抹宽度的快捷键注册
  const minBrushWidth = 1;
  const maxBrushWidth = 100;
  useEffect(() => {
    const hotkey = renderStore.renderHotkey;
    if (!hotkey || !showEraserOpt) {
      return;
    }

    const clamped = (value: number) => {
      return Math.min(maxBrushWidth, Math.max(minBrushWidth, value));
    };

    const increaseBrushWidthHotkey = () => {
      setBrushSize?.(clamped(brushSize + 1));
    };

    const decreaseBrushWidthHotKey = () => {
      setBrushSize?.(clamped(brushSize - 1));
    };

    hotkey.registerHotKey("=", increaseBrushWidthHotkey);
    hotkey.registerHotKey("-", decreaseBrushWidthHotKey);

    return () => {
      hotkey.unregisterHotKey("=", increaseBrushWidthHotkey);
      hotkey.unregisterHotKey("-", decreaseBrushWidthHotKey);
    };
  }, [
    showEraserOpt,
    renderStore.renderHotkey,
    minBrushWidth,
    maxBrushWidth,
    brushSize,
    setBrushSize,
  ]);

  useEffect(() => {
    const hotkey = renderStore.renderHotkey;
    if (!showEraserOpt || !hotkey) {
      return;
    }

    const exitHotkeyHandler = () => {
      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
    };

    hotkey.registerHotKey("esc", exitHotkeyHandler);

    return () => {
      hotkey.unregisterHotKey("esc", exitHotkeyHandler);
    };
  }, [showEraserOpt, headerActionStore, renderStore.renderHotkey]);

  useEffect(() => {
    if (showEraserOpt) {
      initBrush();
    } else {
      clearSelection();
      destroyBrush();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showEraserOpt]);

  // 清除选区
  const clearSelection = () => {
    brush?.clearTargetElement();
  };

  // 初始化橡皮擦
  const initBrush = () => {
    if (!render?._FC) return;

    const brush = new PathBrush(render._FC, {
      shapeColor: "#53F6B4",
      shapeContainerName: EraserContainer, // 承载图形容器名称
      shapeContainerOpacity: 0.3, // 承载图形容器透明度
      showShapeTitle: false, // 是否显示图形标题
      showShapeCloseBtn: false, // 是否显示图形删除按钮
      targetElement: image as IImage, // 承载图形元素
      erase: false, // 是否擦除
      width: brushSize,
      eraseShapeColor: "#FA99FF",
      eraseShapeOpacity: 0.3,
    });
    render._FC.isDrawingMode = true;
    render._FC.freeDrawingBrush = brush;
    setBrush(brush);
  };

  // 销毁橡皮擦
  const destroyBrush = () => {
    if (!render || !brush) return;
    render._FC.isDrawingMode = false;
    render._FC.freeDrawingBrush = undefined;
    brush?.destroy();
    setBrush(undefined);
    setIsEraser(false);
  };

  // 切换橡皮擦
  const handleChangeIsEraser = (erase: boolean) => {
    setIsEraser(erase);
    brush && brush.setErase(erase);
  };

  useEffect(() => {
    if (!render || !renderStyle || !showEraserOpt) {
      return;
    }

    const cursor = isEraser
      ? RenderMaskCursor.pathMinus(brushSize)
      : RenderMaskCursor.pathPlus(brushSize);
    render._FC.freeDrawingCursor = cursor;
    renderStyle?.setCursorStyle({
      mousedown: cursor,
      move: cursor,
      hover: cursor,
      defaults: cursor,
    });
  }, [render, renderStyle, , isEraser, brushSize, showEraserOpt]);

  if (!image || !imageOptions) {
    return null;
  }

  // 开始消除
  const handleEraser = async () => {
    const projectId = projectsStore.activeProjectId;
    const render = renderStore.render;

    if (disabled || !projectId || !render || !brush || objectLoading) {
      return;
    }

    //#region 埋点
    trackEvent("edit_page_top_function_click", {
      function: Track.FunctionEnum.ObjectRemover,
    });

    const deficit = (availableAmount ?? 0) < (price?.amount ?? 0);
    const options =
      renderStore.render && getElementOptions.call(renderStore.render, image);
    const imageIsCreate =
      !!options?._custom_data_history_?.msgId ||
      options?._custom_data_history_?.isFromTemplatePreview;
    trackEvent("create_btn_click", {
      function: Track.FunctionEnum.ObjectRemover,
      board_info: Track.BoardInfo.NoFrame,
      is_picture_upload: imageIsCreate
        ? Track.IsPictureUpload.Create
        : Track.IsPictureUpload.Upload,
      width: image.getScaledWidth(),
      height: image.getScaledHeight(),
      is_vip: userStore.vipLevel,
      // 消耗美豆为0 上报免费
      credit_balance_sufficient:
        price?.amount === 0
          ? Track.CreditBalanceSufficient.Free
          : // 小号不为0 判断是否足够
          deficit
          ? Track.CreditBalanceSufficient.NotEnough
          : Track.CreditBalanceSufficient.Enough,
    });
    //#endregion

    // 图形开始loading
    const startLoading = () => {
      render.Actions.setLoading({
        type: LoadingType.FADE_IN_TO_OUT,
        text: "Generating...",
        id: imageOptions._id_,
      });
    };

    // 图形loading结束
    const loaded = () => {
      render.Actions.setLoaded(imageOptions._id_);
    };

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== image
      ) {
        return;
      }

      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);

    startLoading();

    // 关闭消除操作栏
    setShowEraserOpt(false);

    const upload = createPureUpload();

    try {
      // 1.获取mask图片
      const p = await brush.exportShapesToBlob({
        isMerge: true,
        ext: "png",
        exportContainerType: EraserContainer,
        backgroundFill: "#000",
        shapeFill: "#fff",
      });
      if (!p) return;
      const blob = Array.isArray(p) ? p[0] : p;
      const imageRes = await upload({ file: blob });
      const maskUrl = imageRes.result?.previewUrl || "";

      // 2. 导出画布上原图的blob
      const clippedInitImageBlob = await render.Actions.exportElementToBlob(
        image as Group,
        {
          includeType: [ElementName.IMAGE, ElementName.TEXT],
          exportType: "png",
          multiplier: 1,
        }
      );
      abortController.signal.throwIfAborted();
      if (!clippedInitImageBlob) {
        return;
      }

      // 4. 上传原图
      const initRes = await upload({ file: clippedInitImageBlob });
      abortController.signal.throwIfAborted();
      const clippedInitImageUrl = initRes.result;
      if (!clippedInitImageUrl?.previewUrl) {
        return;
      }

      // 3.提交任务
      const params = {
        initImage: clippedInitImageUrl.previewUrl,
        maskImage: maskUrl,
        initWatermarkImage: imageOptions.src,
      };
      const res = await submitTask({
        projectId,
        layerId: image._id_,
        taskCategory: AIPoster.TaskCategory.Eraser,
        params,
      });
      editorStatusStore.addActiveTask(res.id);
      const msgId = res.id;

      // 更新美豆
      userStore.refreshMtBeanBalance();

      abortController.signal.throwIfAborted();

      //3. 开始轮询
      await dispatch({
        msgId,
        rootStore,
        shape: image,
        abortController,
        expandCustomData: {
          params,
        },
        t,
      });
    } catch (e: any) {
      if (process.env.NODE_ENV === "development") {
        console.warn("消除出错", e);
      }

      if (abortController.signal.aborted) {
        return;
      }

      if (uploadErrorhandler(e, t)) {
        return;
      }

      if (
        handleSubmitError(e, {
          userStore: userStore,
          openMeiDouRecordsPopup,
        })
      ) {
        return;
      }
      return;
    } finally {
      loaded();
      // 更新美豆
      userStore.refreshMtBeanBalance();
    }
  };

  const eraserPanel = createPortal(
    <div
      className={styles["opt-box-container"]}
      ref={elementRef}
      style={{
        transform: `translate(${translate.x}px, ${translate.y}px)`,
      }}
    >
      <div className={"optBox"}>
        <div className={"leftBox"}>
          <Tooltip
            title={t("EraserOpt.Brush")}
            placement="bottom"
            color="#1D1E23"
            align={{
              offset: [0, 20],
            }}
          >
            <div
              className={classNames("brushIcon", !isEraser && "active")}
              onClick={() => handleChangeIsEraser(false)}
            >
              <BrushIcon />
            </div>
          </Tooltip>
          <Tooltip
            title={t("EraserOpt.Eraser")}
            placement="bottom"
            color="#1D1E23"
            align={{
              offset: [0, 20],
            }}
          >
            <div
              className={classNames("brushIcon", isEraser && "active")}
              onClick={() => handleChangeIsEraser(true)}
            >
              <EraserIcon />
            </div>
          </Tooltip>
        </div>
        <div className={"centerBox"}>
          <Slider
            value={brushSize}
            onChange={(value) => {
              setBrushSize(value);
              brush && brush.setWidth(value);
            }}
            markNum={0}
            min={1}
            max={100}
          />
        </div>
        <div
          className={classNames(
            "rightBox",
            (objectLoading || imageStatus === 2) && "disabled"
          )}
          onClick={handleEraser}
        >
          {Number(price?.amount) > 0 && (
            <div>
              <MtBeanFill />
              <span className={"eraseCount"}>{price?.amount}</span>
            </div>
          )}
          <div className={"eraseBtn"}>{t("Remover")}</div>
        </div>
      </div>
    </div>,
    document.getElementById(editorLayoutContent)!
  );

  return (
    <div className={styles.eraserBox}>
      <Tooltip
        arrow={false}
        rootClassName={styles.actionsItemTips}
        align={{
          offset: [0, -15],
        }}
        title={
          <div className={styles.actionsItemTipsContent}>
            <span className={styles.title}>{t("Object Remover")}</span>
            {price?.amount === 0 ? (
              <div>
                限免权益生效中，剩余{" "}
                <span style={{ color: "#FFB200" }}>{price?.totalFreeNum}</span>{" "}
                次
              </div>
            ) : (
              <div className={styles.price}>
                <span style={{ marginRight: "14px" }}>消耗</span>
                <MtBeanFill />
                <span style={{ color: "#293545", marginLeft: "2px" }}>
                  {price?.amount}
                </span>
              </div>
            )}
          </div>
        }
      >
        <div
          className={classNames("actions-item", showEraserOpt && "selected", {
            disabled,
          })}
          onClick={() => {
            // 如果图形正在loading或者正在生成中，不允许点击
            if (objectLoading || imageStatus === 2) {
              return;
            }
            setShowEraserOpt(!showEraserOpt);
          }}
        >
          <div className="icon-box">
            <Eliminate />
          </div>
          <span className="icon-desc">{t("Object Remover")}</span>
        </div>
      </Tooltip>

      {showEraserOpt && eraserPanel}
    </div>
  );
}

export default observer(EraserAction);
