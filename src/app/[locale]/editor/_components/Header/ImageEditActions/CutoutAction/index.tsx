import { submitTask } from "@/api/aiPoster/task";
import { AIPoster } from "@/api/types/aiPoster/task";
import {
  AiCutout,
  ImageBackgroundRemover,
  MtBeanFill,
} from "@meitu/candy-icons";
import { Tooltip } from "antd";
import classNames from "classnames";
import { observer } from "mobx-react";
import {
  FabricObject,
  getElementOptions,
  LoadingType,
  WarningType,
} from "@meitu/whee-infinite-canvas";
import { useEffect, useRef, useState } from "react";
import { MemberGroupCategory, MtccFuncCode } from "@/types";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { isTextEditing } from "@/app/[locale]/editor/_utils/isTextEditing";
import { handleSubmitError } from "@/app/[locale]/editor/_utils/submit";
import { dispatch } from "@/app/[locale]/editor/_utils/TaskDispatcher";
import useConfirmModal from "@/hooks/useConfirmModal";
import { MaxCutoutLimit } from "@/app/[locale]/editor/_constant/params";
import { HeaderAction } from "@/app/[locale]/editor/_store/headerAction";
import { useGuideCreateProject } from "@/app/[locale]/editor/_context/GuideCreateProjectContext";
import { useI18n } from "@/locales/client";
import { SubscribeModalType } from "@/components/SubscribeModal/types";
import { useStore } from "@/contexts/StoreContext";

import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";

import { fetchPriceDesc } from "@/api/meidou";
import { FunctionCode, MeiDouFetchPriceDescResponse } from "@/api/types/meidou";
import styles from "../index.module.scss";
import useSizeExceedConfirmModal from "@/app/[locale]/editor/_hooks/useSizeExceedConfirmModal";
import { trackEvent } from "@meitu/subscribe-intl";
import { Track } from "@/app/[locale]/editor/_constant/track";
import { useMeiDouBalance } from "@/hooks/useMeidou";

function CutoutAction() {
  const rootStore = useRootStore();
  const {
    renderStore,
    projectsStore,
    selectionStore,
    headerActionStore,
    editorStatusStore,
  } = rootStore;

  const image = selectionStore.singleImage?.image;
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();
  const { userStore } = useStore();
  const imageOptions = selectionStore.singleImage?.options;
  const { availableAmount } = useMeiDouBalance({ userStore });
  const [price, setPrice] = useState<MeiDouFetchPriceDescResponse | null>(null);

  const { createGuideProject, isInGuidePage } = useGuideCreateProject();

  const handleCutoutRef = useRef<any>(null);

  const t = useI18n();

  const customData = imageOptions?._custom_data_history_;

  const imageSrc = imageOptions?.src;
  // 抠图的原图优先使用urlShort（无水印） 对于上传的图片 没有urlShort 使用src即可
  const initImage = customData?.urlShort ?? imageOptions?.src;

  const objectLoading = imageOptions?._loading_;
  const hasCutout =
    typeof customData?.hasCutout === "boolean" && customData.hasCutout;

  const imageStatus = customData?.imageStatus;

  // 如果图片正处于loading状态 或者已经抠图 则不能再次抠图
  const disabled =
    !image ||
    objectLoading ||
    hasCutout ||
    imageStatus === AIPoster.ImageStatus.AuditFailed;

  // const openSubscribePopup = useOpenSubscribePopup();

  // 获取抠图美豆数量
  useEffect(() => {
    if (!image) {
      return;
    }

    let ignore = false;

    fetchPriceDesc({
      functionCode: FunctionCode.aiPosterCutout,
      functionBody: JSON.stringify({}),
    }).then((res) => {
      if (ignore) {
        return;
      }
      // setPrice(res.amount);
      setPrice(res);
    });

    return () => {
      ignore = true;
    };
  }, [image]);

  // 快捷键
  useEffect(() => {
    const renderHotkey = renderStore.renderHotkey;
    if (disabled || !renderHotkey) return;

    const hotkeyHandler = (event: KeyboardEvent) => {
      if (isTextEditing(renderStore.render)) {
        return;
      }
      event.preventDefault();
      handleCutoutRef.current?.();
    };

    renderHotkey.registerHotKey("shift + r", hotkeyHandler);

    return () => {
      renderHotkey.unregisterHotKey("shift + r", hotkeyHandler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [renderStore.renderHotkey, disabled]);

  const onConfirm = () => {
    handleCutout();
  };

  const { open, contextHolder, getNeedsOpen } = useSizeExceedConfirmModal({
    feature: AIPoster.TaskCategory.Cutout,
    title: "Do you want to continue?",
    description:
      "The image is too large, processing it will affect the resolution.",
    onConfirm,
  });

  if (!image || !imageOptions || !imageSrc) {
    return null;
  }

  const handleCutoutPreset = async () => {
    if (disabled) return;

    //#region 埋点
    trackEvent("edit_page_top_function_click", {
      function: Track.FunctionEnum.BgRemover,
    });

    const deficit = (availableAmount ?? 0) < (price?.amount ?? 0);
    const options =
      renderStore.render && getElementOptions.call(renderStore.render, image);
    const imageIsCreate =
      !!options?._custom_data_history_?.msgId ||
      options?._custom_data_history_?.isFromTemplatePreview;
    trackEvent("create_btn_click", {
      function: Track.FunctionEnum.BgRemover,
      board_info: Track.BoardInfo.NoFrame,
      is_picture_upload: imageIsCreate
        ? Track.IsPictureUpload.Create
        : Track.IsPictureUpload.Upload,
      width: image.getScaledWidth(),
      height: image.getScaledHeight(),
      is_vip: userStore.vipLevel,
      // 消耗美豆为0 上报免费
      credit_balance_sufficient:
        price?.amount === 0
          ? Track.CreditBalanceSufficient.Free
          : // 小号不为0 判断是否足够
          deficit
          ? Track.CreditBalanceSufficient.NotEnough
          : Track.CreditBalanceSufficient.Enough,
    });
    //#endregion

    if (!projectsStore.activeProjectId) {
      if (!isInGuidePage) {
        return;
      }

      await createGuideProject();
    }

    if (
      Math.max(imageOptions?.width, imageOptions?.height) > MaxCutoutLimit &&
      getNeedsOpen()
    ) {
      open();
    } else {
      handleCutout();
    }
  };

  const handleCutout = async () => {
    if (disabled) {
      return;
    }

    const projectId = projectsStore.activeProjectId;
    const render = renderStore.render;
    const historyPlugins = renderStore.historyPlugins;
    if (!projectId || !render || !historyPlugins) {
      return;
    }

    // 退出其他操作的编辑态
    headerActionStore.setActiveHeaderAction(HeaderAction.Cursor);

    // 图形开始loading
    const startLoading = () => {
      render.Actions.setLoading({
        type: LoadingType.FADE_IN_TO_OUT,
        text: "Generating...",
        id: imageOptions._id_,
      });
    };

    // 图形loading结束
    const loaded = () => {
      render.Actions.setLoaded(imageOptions._id_);
    };

    // 图形loading
    const operation = historyPlugins.baseAction.getLoadOperation([image]);

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== image
      ) {
        return;
      }

      // 更新loading状态
      // selectionStore.updateOptions();
      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);

    operation && historyPlugins.submit(operation);
    startLoading();

    let msgId = "";
    const params = {
      initImage: initImage,
      linkMsgId: customData?.msgId,
      initWatermarkImage: imageSrc,
    };

    try {
      // 创建任务
      const res = await submitTask({
        taskCategory: AIPoster.TaskCategory.Cutout,
        projectId,
        params,
        functionName: MtccFuncCode.FuncCodePosterCutout,
      });
      editorStatusStore.addActiveTask(res.id);
      msgId = res.id;
    } catch (err) {
      handleSubmitError(err, {
        userStore: userStore,
        openMeiDouRecordsPopup,
      });

      loaded();
      return;
    } finally {
      // 更新美豆
      userStore.refreshMtBeanBalance();
    }

    // 如果任务创建失败 或者任务被中断 不继续执行
    if (!msgId || abortController.signal.aborted) {
      return;
    }

    return dispatch({
      msgId,
      rootStore,
      shape: image,
      abortController,
      expandCustomData: {
        params,
      },
      t,
    }).finally(() => {
      // 更新美豆
      userStore.refreshMtBeanBalance();
    });
  };
  handleCutoutRef.current = handleCutoutPreset;
  return (
    <>
      <Tooltip
        arrow={false}
        rootClassName={styles.actionsItemTips}
        align={{
          offset: [0, -15],
        }}
        title={
          <div className={styles.actionsItemTipsContent}>
            <span className={styles.title}>{t("Background Remover")}</span>
            {price?.amount === 0 ? (
              <div>
                限免权益生效中，剩余{" "}
                <span style={{ color: "#FFB200" }}>{price?.totalFreeNum}</span>{" "}
                次
              </div>
            ) : (
              <div className={styles.price}>
                <span>消耗</span>
                <MtBeanFill />
                <span style={{ color: "#293545" }}>{price?.amount}</span>
              </div>
            )}
          </div>
        }
      >
        <div
          className={classNames("actions-item", { disabled })}
          onClick={handleCutoutPreset}
        >
          <div className="icon-box">
            <ImageBackgroundRemover />
          </div>
          <span className="icon-desc">{t("Background Remover")}</span>
        </div>
      </Tooltip>
      {contextHolder}
    </>
  );
}

export default observer(CutoutAction);
