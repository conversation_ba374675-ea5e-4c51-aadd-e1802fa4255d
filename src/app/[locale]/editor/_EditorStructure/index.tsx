"use client";

import { Fragment, useCallback, useEffect, useRef } from "react";
import styles from "./index.module.scss";
import { observer } from "mobx-react-lite";
import { useStore } from "@/contexts/StoreContext";
import { trackEvent } from "@meitu/subscribe-intl";
import "driver.js/dist/driver.css";
import { Driver } from "driver.js";
import { useRootStore } from "../_store";
import { useTutorials } from "../_hooks/useTutorials";
import { FullScreenLoading } from "../_components/FullScreenLoading";
import TemplateAndHistory from "../_components/TemplateAndHistory";
import Editor from "../_components/Editor";
import ActionBar from "../_components/ActionBar";
import { ShortcutHelper } from "..//_components/ShortcutHelper";
import ImageBar from "../_components/ImageBar";
import Create from "../_components/Create";
import LayerPanel from "../_components/LayerPanel";
import ParamsSidebar from "../_components/ParamsSidebar";
import { editorLayoutContent } from "../_constant/element";
import ParamsSidebarContextProvider from "../_components/ParamsSidebar/context";

type EditorStructureProps = React.PropsWithChildren;

function EditorStructure({ children }: EditorStructureProps) {
  const rootStore = useRootStore();
  const { editorStatusStore, renderStore, selectionStore, projectsStore } = useRootStore();
  const { userStore } = useStore();
  const driverObjRef = useRef<Driver | null>(null);

  const loading =
    !userStore.initFinish ||
    editorStatusStore.changeProjectLoading ||
    !editorStatusStore.initFinish;

  const elementRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    editorStatusStore.editorLayoutContainer = elementRef.current;
  });

  // 初始化首次引导教程
  const handleTutorialInitialized = useCallback((driver: Driver) => {
    driverObjRef.current = driver;
  }, []);

  useTutorials(loading, rootStore, handleTutorialInitialized);

  useEffect(() => {
    // mtstat 还没有初始化
    setTimeout(() => {
      trackEvent("edit_page_enter");
    }, 50);

    // 组件卸载时销毁 driver 实例
    return () => {
      driverObjRef.current?.destroy();
    };
  }, []);

  useEffect(() => {
    if (!loading) {
      renderStore.render?._FC?.set("backgroundColor", "transparent");
      renderStore.render?._FC?.requestRenderAll();
    }
  }, [loading]);

  return (
    <Fragment>
      <FullScreenLoading loading={loading} />
      <div
        className={styles.contentBox}
        id={editorLayoutContent}
        ref={elementRef}
      >
        <ParamsSidebarContextProvider
          selectionStore={selectionStore}
          renderStore={renderStore}
        >
          <ParamsSidebar />
          <TemplateAndHistory />
        </ParamsSidebarContextProvider>
        {/* 图层面板 */}
        {/* <LayerPanel /> */}
        <Editor />
        <ActionBar />
        <ShortcutHelper />
        <ImageBar />
        {children}
      </div>
    </Fragment>
  );
}

export default observer(EditorStructure);
