"use client";
import { useStore } from "@/contexts/StoreContext";
import { observer } from "mobx-react-lite";
import { useEffect } from "react";
import { useRootStore } from "./_store";
import { useRouter } from "next/navigation";
import { fetchProjectDetail } from "@/api/aiPoster/project";

function Page() {

  const { userStore } = useStore();
  const { projectsStore, editorStatusStore } =
    useRootStore();
  const router = useRouter();

  useEffect(() => {
    if (editorStatusStore.initFinish || !userStore.initFinish) {
      return;
    }

    if (!editorStatusStore.initFinish && !userStore.isReady) {
      editorStatusStore.setInitFinish(true);
      return;
    }

    let ignore = false;
    editorStatusStore.globalEnable();
    projectsStore
      .fetchProjects()
      .then(async () => {
        
        if (!projectsStore.projects.length) {
          throw new Error("no projects");
        }

        try {
          return await fetchProjectDetail({ projectId: projectsStore.projects[0].id });
        } catch (e) {
          throw e;
        }
      })
      // 选择一个项目并加载
      .then((projectDetail) => {
        if (ignore) {
          return;
        }

        return projectsStore
          .setActiveProject(projectDetail)
          .then(() => {
            if (ignore) {
              return;
            }

            router.replace(`/editor/${projectDetail.projectId}`);
          });
      })
      .catch((e) => {
        if (process.env.NODE_ENV === "development") {
          console.log(e);
        }
      })
      .finally(() => {
        if (ignore) {
          return;
        }
        editorStatusStore.setInitFinish(true);
      });

    return () => {
      ignore = true;
    }
  }, [userStore.initFinish, userStore.isReady]);
  
  return null;
}

export default observer(Page)