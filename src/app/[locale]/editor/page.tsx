"use client";
import { useStore } from "@/contexts/StoreContext";
import { observer } from "mobx-react-lite";
import { useEffect } from "react";
import { useRootStore } from "./_store";

function Page() {

  const { userStore } = useStore();
  const { editorStatusStore } = useRootStore();

  useEffect(() => {
    if (!userStore.initFinish) {
      return;
    }

    editorStatusStore.setInitFinish(true);
  }, [userStore.initFinish])
  
  return null;
}

export default observer(Page)