import { useRootStore } from '../_store';
import { useStore } from '@/contexts/StoreContext';
import { useCreateProject } from '@/hooks/useCreateProject';
import { useRouter } from 'next/navigation';
import { useEffect, useRef } from 'react';
import { ChangeProjectOptions } from '../_store/projects';
import { SaveProjectRequest } from '@/api/types/aiPoster/project';
import _ from 'lodash';


type CreateProjectOptions = {
  project?: Partial<Omit<SaveProjectRequest, "projectId">>
} & ChangeProjectOptions;

/**
 * 在编辑器中创建新项目
 * 
 * 1. 创建新项目后会自动修改path到/editor/project/{projectId}
 * 2. 创建项目会验证商业化逻辑
 */
export function useCreateProjectInEditor() {
  const { globalProjectStore, userStore } = useStore();
  const { projectsStore } = useRootStore();

  const { createProject: commonCreateProject } = useCreateProject({ userStore, globalProjectStore });
  const router = useRouter();

  // 用来标记页面路径修改 如果路径修改 跳转到了其他页面 需要取消后续的程序化路由
  const pathChangeFlag = useRef(false);
  useEffect(() => {
    return () => {
      pathChangeFlag.current = true;
    };
  }, [])


  const createProject = (createProjectOptions?: CreateProjectOptions) => {
    return commonCreateProject(createProjectOptions?.project)
      .then(res => {
        if (!res || pathChangeFlag.current) {
          return;
        }
        projectsStore.setActiveProject({ ...res, canEdit: true }, _.omit(createProjectOptions, 'project'));
        router.replace(`/editor/${res.projectId}`);
        return res;
      });
  };

  return {
    createProject,
  };
};
