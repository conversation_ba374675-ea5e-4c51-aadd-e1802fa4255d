import { useRootStore } from "../_store";
import { uploadErrorhandler } from "@/utils/error-handler/upload"
import { insertImageToViewPortCenter } from "../_utils/insertImage";
import { createImage, Group, Point } from "@meitu/whee-infinite-canvas";
import { useSubmitAddHistory } from "./useSaveProjectHistory";
import { useMonitorUploadFunc } from "@/hooks/useMonitorUploadFunc";
import { useI18n } from "@/locales/client";
import { saveProject } from "@/api/aiPoster/project";
import { UploadContext } from "@/utils/uploader";


type UploadOptions = {
  /**
   * 是否记录历史
   */
  recordHistory?: boolean;
  /**
   * 是否记录上传状态（用来展示loading）
   */
  recordUploadingDesc?: boolean;
  /**
   * 如果传了该拖拽事件 则表示是拖动上传
   */
  dragEvent?: React.DragEvent<HTMLDivElement>;
  /**
   * 
   */
  extendsCustomDataHistory?: Record<string, any>;
}

const defaultUploadOptions: UploadOptions = {
  recordHistory: true,
  recordUploadingDesc: true,
}

type UseUploadOptions = {
  upload?: (context: UploadContext) => Promise<UploadContext>; 
}

export function useUpload(options?: UseUploadOptions) {

  const defaultUpload = useMonitorUploadFunc();
  const upload = options?.upload ?? defaultUpload;
  const rootStore = useRootStore();
  const { renderStore, editorStatusStore, projectsStore } = rootStore;
  const render = renderStore.render;
  const recordHistory = useSubmitAddHistory(rootStore);
  const t = useI18n();
  const projectId = projectsStore.activeProjectId;

  return async (uploadedFiles: File | File[], options?: UploadOptions) => {
    const files = Array.isArray(uploadedFiles) ? uploadedFiles : [uploadedFiles];

    if (!upload || !render) {
      return;
    }

    const mergedOptions = {
      ...defaultUploadOptions,
      ...options,
    }

    // 如果传入了dragEvent 则表示是一个拖动上传
    // 计算要插入的位置
    let dragPoint = null as null | Point;
    if (mergedOptions.dragEvent) {
      const dragPosition = render._FC.getScenePoint(mergedOptions.dragEvent as any);
      dragPoint = new Point(dragPosition.x, dragPosition.y);
    }

    // 每个文件的单词上传
    const uploadSingle = async (file: File) => {

      // 1. 上传
      let url = '';
      try {
        const context = await upload({
          file,
        });
  
        url = context.result?.previewUrl ?? '';
      } catch(e: any) {
        uploadErrorhandler(e, t);
      }
  
      if (!url) {
        return;
      }

      // 2. 插入到编辑器
      let group = null as null | Group;
      if (dragPoint) {
        group = await createImage('', { src: url });
        render.add(group);
        group.setXY(dragPoint);
        render._FC.setActiveObject(group);
      } else {
        group = await insertImageToViewPortCenter({ src: url, _custom_data_history_: { ...options?.extendsCustomDataHistory } }, render);
        render._FC.setActiveObject(group);
      }

      // 3. 记录历史
      if (group && mergedOptions.recordHistory) {
        const project = await recordHistory([group]);
        /**
         * 在记录历史时 如果当前是“游客”模式 则会创建一个新的项目
         * 在这个时候 projectId是0
         * 因此，不能直接使用projectId，需要使用新创建的项目的id
         */
        

        const proId = project?.projectId || projectId;
        if (proId) {
          await saveProject({
            projectId: proId,
            picUrl: url,
          })
        }
      }

      return group;
    }

    // 记录上传状态
    const uploadKey = Symbol();
    if (mergedOptions.recordUploadingDesc) {
      editorStatusStore.appendUploadingDescription({ key: uploadKey })
    }
    const results = await Promise.allSettled(files.map(uploadSingle));
    // 上传完成后移除上传状态
    editorStatusStore.deleteUploadingDescription(uploadKey);
    const groups = results.reduce((acc, r) => {
      if (r.status === 'fulfilled' && r.value) {
        acc.push(r.value);
      }

      return acc;
    }, [] as Group[]);
    return groups;
  }
}