import { makeAutoObservable, runInAction, toJS } from "mobx";
import { RootStore } from ".";
import {
  fetchProjectConfig,
  fetchProjectDetail,
  saveProject,
} from "@/api/aiPoster/project";
import { defaultErrorHandler } from "@/utils/defaultErrorHandler";
import {
  ElementName,
  FabricObject,
  getElementOptions,
  LoadingType,
} from "@meitu/whee-infinite-canvas";
import { debounce, merge } from "lodash";
import { AIPoster } from "@/api/types/aiPoster/task";
import { dispatch } from "../_utils/TaskDispatcher";
import zoomToFitCanvas from "../_utils/zoomToFitCanvas";
import { handleProjectDeletedError } from "../_utils/projectDeletedErrorHandler";
import { needsProcessImage } from "../_utils/TaskDispatcher/needsProcessImage";
import { backfillEditorParams } from "../_utils/backfillEditorParams";
import { ProjectDetailResponse } from "@/api/types/aiPoster/project";
import { rootStore as globalRootStore } from "@/stores/RootStore";
export type ProjectItem = {
  id: number;
  name: string;
  previewImage: string;
  canEdit: boolean;
};

/**
 * 创建项目时的选项
 */
type CreateProjectOptions = {
  /**
   * 项目名
   */
  name?: string;
  /**
   * 创建完成后是否自动选择到新的项目
   */
  autoChange?: boolean;
  /**
   * 异常处理
   */
  errorHandler?(e: any): void;
};

/**
 * 改变项目时的选项
 */
export type ChangeProjectOptions = {
  /**
   * 是否进行回填操作
   */
  backfill?: boolean;
};

export class ProjectsStore {
  rootStore: RootStore;

  projects: Array<ProjectItem> = [];
  activeProject: ProjectItem | null = null;
  refreshing: boolean = false;

  get activeProjectId() {
    return this.activeProject?.id || 0;
  }

  get projectsList() {
    const projects = this.projects;
    const activeProject = this.activeProject;

    if (!activeProject) {
      return projects;
    }

    const otherProjects = projects.filter((p) => p.id !== activeProject.id);
    return [activeProject, ...otherProjects];
  }

  private static DefaultChangeProjectOptions: ChangeProjectOptions = {
    backfill: true,
  };

  async changeProject(id: number, options?: ChangeProjectOptions) {

    if (id === this.activeProjectId) {
      return;
    }

    this.rootStore.pollingManagers.tasksPollingManager.stop();

    const startLoading = () => {
      this.rootStore.editorStatusStore.setChangeProjectLoading(true);
    };

    const endLoading = () => {
      this.rootStore.editorStatusStore.setChangeProjectLoading(false);
    };

    startLoading();
    try {
      const detail = await fetchProjectDetail({ projectId: id });
      this.rootStore.renderStore.historyPlugins?.history.clear();
      await this.setActiveProject(detail, options);
    } catch (e) {
      if (process.env.NODE_ENV === "development") {
        console.error(`未找到项目: projectId[${id}]`);
      }
    } finally {
      endLoading();

    }
  }

  async setActiveProject(item: ProjectDetailResponse, options?: ChangeProjectOptions) {
    const newItem: ProjectItem = {
      id: item.projectId,
      previewImage: item.picUrl ?? "",
      name: item.projectName,
      canEdit: item.canEdit,
    };

    runInAction(() => {

      if (!this.projectsList.find((p) => p.id === item.projectId)) {
        this.projects = [newItem, ...toJS(this.projects)];
      } else {
        this.projects = this.projects.map(p => {
          if (!p.id) {
            return p;
          }
          return newItem;
        });
      }
    })

    this.activeProject = newItem;

    const mergedOptions = {
      ...ProjectsStore.DefaultChangeProjectOptions,
      ...options,
    };

    if (mergedOptions.backfill) {
      try {
        await this.backfillProjectDetail(item);
      } catch (e) {
        if (process.env.NODE_ENV === "development") {
          console.error(`项目回填失败: projectId[${item.projectId}]`, e);
        }
      }

      try {
        await saveProject({ projectId: item.projectId });
      } catch (e) {
        if (process.env.NODE_ENV === "development") {
          console.error(`项目回填失败: projectId[${item.projectId}]`, e);
        }
      } finally {
        globalRootStore.globalProjectStore.setProjectSaveSuccess(true);
      }
    }    
  }

  public disableEditable() {
    if (!this.activeProject) {
      return;
    }

    this.activeProject = {
      ...toJS(this.activeProject),
      canEdit: false,
    }
  }

  public get currentCanEdit() {
    return !!this.activeProject?.canEdit;
  }

  /**
   * 获取项目
   */
  fetchProjects() {
    return fetchProjectConfig()
      .then((res) => {
        const { projectList, config } = res;
        runInAction(() => {
          this.projects = projectList.map((p) => {
            return {
              id: p.projectId,
              name: p.projectName,
              previewImage: p.picUrl ?? "",
              canEdit: true,
            };
          });
        });
      })
      .catch((e) => {
        defaultErrorHandler(e);
      });
  }

  async refreshProjects() {
    this.refreshing = false;
    await this.fetchProjects();
    const activeProjectId = this.activeProjectId;

    if (!activeProjectId) {
      runInAction(() => {
        this.activeProject = null;
      });
  
      return;
    }

    try {
      const detail = await fetchProjectDetail({ projectId: activeProjectId });
      if (this.activeProjectId !== activeProjectId) {
        return;
      }

      runInAction(() => {
        this.activeProject = {
          id: detail.projectId,
          name: detail.projectName,
          previewImage: detail.picUrl ?? "",
          canEdit: !!detail.canEdit,
        };
        this.refreshing = false;
      });
    } catch (e: any) {
      if (process.env.NODE_ENV === "development") {
        console.error(`未找到项目: projectId[${activeProjectId}]`, e);
      }
      handleProjectDeletedError(e);
    }
  }

  /**
   * 重命名项目
   * @param id
   * @param newName
   */
  async renameProject(id: number, newName: string) {
    const changeName = (id: number, name: string) => {
      let preName = "";

      runInAction(() => {
        this.projects = this.projects.map((p) => {
          if (p.id !== id) {
            return p;
          }

          preName = p.name;

          return {
            ...p,
            name,
          };
        });

        if (id === this.activeProjectId && this.activeProject) {
          preName = this.activeProject.name;

          this.activeProject = {
            ...toJS(this.activeProject),
            name,
          };
        }
      });
      // 如果项目名没有发生改变 则不需要恢复
      if (preName === name) {
        return;
      }

      // 如果项目名修改失败 恢复到之前的项目名
      return () => {
        if (!preName) {
          return;
        }

        changeName(id, preName);
      };
    };

    const restore = changeName(id, newName);

    // 项目名没有发生变化 不需要同步服务端
    if (!restore) {
      return;
    }

    try {
      await saveProject({
        projectId: id as number,
        projectName: newName,
      });
    } catch (e) {
      defaultErrorHandler(e);
      // 项目名修改失败 恢复到之前的项目名
      restore();
      return;
    }
  }

  /**
   * 改变预览图
   * @param id
   * @param newImage
   */
  async changePreviewImage(id: number, newImage: string) {
    const changeName = (id: number, previewImage: string) => {
      let prevImage = "";

      runInAction(() => {
        this.projects = this.projects.map((p) => {
          if (p.id !== id) {
            return p;
          }

          prevImage = p.previewImage;

          return {
            ...p,
            previewImage,
          };
        });

        if (id === this.activeProjectId && this.activeProject) {
          prevImage = this.activeProject.previewImage;

          this.activeProject = {
            ...toJS(this.activeProject),
            previewImage,
          };
        }
      });
      // 如果项目名没有发生改变 则不需要恢复
      if (prevImage === newImage) {
        return;
      }

      // 如果项目名修改失败 恢复到之前的项目名
      return () => {
        if (!prevImage) {
          return;
        }

        changeName(id, prevImage);
      };
    };

    const restore = changeName(id, newImage);

    // 项目名没有发生变化 不需要同步服务端
    if (!restore) {
      return;
    }

    try {
      await saveProject({
        projectId: id as number,
        picUrl: newImage,
      });
    } catch (e) {
      defaultErrorHandler(e);
      // 项目名修改失败 恢复到之前的项目名
      restore();
      return;
    }
  }

  // private static defaultCreateProjectOptions: CreateProjectOptions = {
  //   name: "未命名",
  //   autoChange: true,
  // };
  // /**
  //  * 创建新项目
  //  * @param name
  //  */
  // async createProject(
  //   options: CreateProjectOptions = ProjectsStore.defaultCreateProjectOptions
  // ) {
  //   options = merge(ProjectsStore.defaultCreateProjectOptions, options);
  //   const { name, autoChange, errorHandler } = options;

  //   try {
  //     const res = await saveProject({
  //       projectName: name,
  //       picUrl: "",
  //     });

  //     const newProject: ProjectItem = {
  //       id: res.projectId,
  //       name: res.projectName,
  //       previewImage: "",
  //     };
  //     // await this.fetchProjects();
  //     runInAction(() => {
  //       this.projects = [newProject, ...toJS(this.projects)];
  //     });

  //     if (autoChange) {
  //       this.rootStore.renderStore.render?.clear();
  //       await this.changeProject(res.projectId);
  //     }

  //     return res;
  //   } catch (e) {
  //     if (process.env.NODE_ENV === "development") {
  //       console.error("任务创建失败", e);
  //     }

  //     errorHandler?.(e);
  //   }
  // }

  /**
   * 回填项目详情
   */
  private async backfillProjectDetail(detail?: ProjectDetailResponse) {
    const render = this.rootStore.renderStore.render;
    if (!render) {
      return;
    }

    this.rootStore.renderStore.render?.clear();
    try {
      let res = detail;

      if (!res) {
        res = await fetchProjectDetail({
          projectId: this.activeProjectId,
        });
      }

      if (!res?.editorParams) return;
      const editorParams = res.editorParams;
      
      await backfillEditorParams(editorParams, { render });
      zoomToFitCanvas(render);
      await this.backfillLoading();
    } catch (e) {
      defaultErrorHandler(e);
    }
  }

  private async backfillLoading() {
    const renderStore = this.rootStore.renderStore;
    const { render } = renderStore;

    if (!render) {
      return;
    }
    const msgIds = new Set<string>();

    // 收集画布中需要loading的图形
    const reverse = (shape: FabricObject) => {
      const options = getElementOptions.call(render, shape);
      if (options._name_ === ElementName.IMAGE) {
        if (options._loading_ === true) {
          const customData = options._custom_data_history_ ?? {};
          const msgId = customData.msgId as string | undefined;
          if (!msgId) {
            render.Actions.setLoaded(options._id_);
            return;
          }

          render.Actions.setLoading({
            type: LoadingType.FADE_IN_TO_OUT,
            text: "Generating...",
            id: options._id_,
          });
          msgIds.add(msgId);
        }
        return;
      }
    };
    render._FC.getObjects().forEach(reverse);

    // 收集任务中需要loading的图形
    const recordsStore = this.rootStore.generateRecordStore;
    await recordsStore.fetchHistory({ enterStatus: "updating" });
    toJS(recordsStore.list).forEach((task) => {
      if (task.loadingStatus === AIPoster.LoadingStatus.Loading) {
        return msgIds.add(task.id);
      }
      // 对于已经成功的任务
      // 1. 抠图需要将mask图与原图合成结果图，然后上传、替换
      // 2. 超清需要使用原图的alpha通道替换结果图alpha通道，然后上传、替换
      if (needsProcessImage(task)) {
        return msgIds.add(task.id);
      }
    });

    // 派发轮训
    msgIds.forEach((id) => {
      dispatch({
        rootStore: this.rootStore,
        msgId: id,
        t: this.rootStore.t,
      });
    });
  }

  constructor(rootStore: RootStore) {
    makeAutoObservable(this);
    this.rootStore = rootStore;

    //#region 更新封面图
    const debouncedSave = debounce(
      (projectId: number, picUrl: string) => {
        this.changePreviewImage(projectId, picUrl);
      },
      3000,
      {
        leading: false,
        trailing: true,
      }
    );

    const handleSavePreviewImage = (payload: { resolved: AIPoster.Task[] }) => {
      const projectId = this.activeProjectId;
      if (!projectId) {
        return;
      }

      const successTask = payload.resolved.find(
        (task) => task.loadingStatus === AIPoster.LoadingStatus.Success
      );
      const successImage = successTask?.resultImages.find(
        (image) => image.imageStatus === AIPoster.ImageStatus.Success
      );
      if (!successImage) {
        return;
      }

      const picUrl = successImage.urlSign;
      debouncedSave(projectId, picUrl);
    };
    rootStore.pollingManagers.tasksPollingManager.addTaskResolvedListener(
      handleSavePreviewImage
    );
    //#endregion 更新封面图
  }
}
