import {
  MEMBER_EXPIRED_CODE,
  MEMBER_EXPIRED_CODE_PLUS,
  TEXT_CHANGE_VIP_CODE,
  UNABLE_CONSUME_CODE,
  UNLIMIT,
} from "@/constants/errorCode";
import { defaultErrorHandler } from "@/utils/defaultErrorHandler";
import { RootStore } from "../_store";
import { SubscribeModalType } from "@/components/SubscribeModal/types";
import { useStore } from "@/contexts/StoreContext";
import toast from "@/components/Toast";
import { AIPoster } from "@/api/types/aiPoster/task";
import { MemberGroupCategory } from "@/types";
import { UserStore } from "@/stores/UserStore";

// 处理提交任务失败
export const handleSubmitError = (
  err: any,
  {
    userStore,
    openMeiDouRecordsPopup,
  }: {
    userStore: UserStore;
    openMeiDouRecordsPopup: (
      type?: MemberGroupCategory,
      functionId?: string
    ) => void;
  }
) => {
  const functionId =
    AIPoster.TaskFunctionCode[
      err?.response?.headers
        ?.task_category as keyof typeof AIPoster.TaskFunctionCode
    ];

  if (err?.response?.data?.code === UNABLE_CONSUME_CODE) {
    if (userStore.isVipCurrent) {
      openMeiDouRecordsPopup(MemberGroupCategory.Meidou, functionId);
    } else {
      openMeiDouRecordsPopup(MemberGroupCategory.Member, functionId);
    }
    return true;
  } else {
    defaultErrorHandler(err);
    return false;
  }

  // if (
  //   err?.response?.data?.code === MEMBER_EXPIRED_CODE ||
  //   err?.response?.data?.code === MEMBER_EXPIRED_CODE_PLUS
  // ) {
  //   // 会员到期，项目转化弹窗
  //   // rootStore.paramsEditorStore.setOpenProjectMemberModal(true);
  //   subscribeModal.open({
  //     productType: SubscribeModalType.Project,
  //     functionId,
  //   });
  //   rootStore.projectsStore.disableEditable();
  //   return true;
  // } else if (err?.response?.data?.code === UNABLE_CONSUME_CODE) {
  //   // 美豆不足，充值弹窗
  //   // 判断用户vip 等级 如果是plus 用户，弹出美豆充值，其他 弹出订阅弹框
  //   if (userStore?.vipLevel === 2) {
  //     openMeiDouRecordsPopup(MemberGroupCategory.Meidou, functionId);
  //     return true;
  //   } else {
  //     // openSubscribe?.();
  //     subscribeModal.open({
  //       productType: SubscribeModalType.Basic,
  //       functionId,
  //     });
  //     return true;
  //   }
  //   return true;
  // } else if (err?.response?.data?.code === UNLIMIT) {
  //   // 并行任务限制
  //   if (userStore?.vipLevel === 2) {
  //     toast.error(err?.response?.data?.message);
  //   } else {
  //     subscribeModal.open({
  //       productType: SubscribeModalType.Concurrent,
  //       functionId,
  //     });
  //     return true;
  //   }

  //   return true;
  // } else if (err?.response?.data?.code === TEXT_CHANGE_VIP_CODE) {
  //   // 改字
  //   toast.error(err?.response?.data?.message);
  //   setTimeout(() => {
  //     subscribeModal.open({
  //       productType: SubscribeModalType.Basic,
  //       functionId,
  //     });
  //   }, 3000);

  //   return true;
  // } else {
  //   defaultErrorHandler(err);
  //   return false;
  // }
};
