import type { <PERSON>ada<PERSON> } from "next";
import type { Viewport } from "next";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { InitClient } from "@/components/InitClient";
import { GlobalRouteTracker } from "@/components/GlobalRouteTracker";
import "./global.css";
import { StoreProvider } from "@/contexts/StoreContext";
import AntdConfigProvider from "@/components/AntdConfigProvider";
import { I18nProviderClient } from "../../locales/client";
import { SubscribeModalProvider } from "@/contexts/SubscribeModalContext";
import { QuestionProvider } from "@/contexts/QuestionContext";
import { SubscribeModal } from "@/components/SubscribeModal";
import { ModalStoreConsumer } from "@/stores/ModalStore";
import { Tracer } from "@/components/Tracer";
import "@meitu/subscribe-intl/dist/style.css";
// import '@meitu/subscribe-intl/dist/themes/dark.css';
import "@/styles/dark.css";
import { Poppins } from 'next/font/google';
import { GTMInit, GTMNoScript } from "@/components/GtmScript"; 
// import './global.scss';
export const metadata: Metadata = {
  title: {
    default: "WheeAI - AI Poster Maker Generate and Edit", // 默认 title（如果某个页面没设置 title）
    template: "WheeAI - %s", // 页面自定义 title 时使用该模板
  },
  description:
    "WheeAI is an online AI Poster and Image Generator. Create pro designs in seconds with enormous templates.Try free!",
  openGraph: {
    title: "WheeAI - Online AI Poster Generator",
    description: "Create pro designs in seconds—no design experience needed.",
    url: "https://whee.ai",
    siteName: "WheeAI",
    images: [
      {
        url: "https://wheeai-public.stariidata.com/static/open-graph/image.jpg",
        width: 1200,
        height: 630,
        alt: "WheeAI - Online AI Poster Generator",
        type: "image/jpeg",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "WheeAI - Online AI Poster Generator",
    description: "Create pro designs in seconds—no design experience needed.",
    images: [
      "https://wheeai-public.stariidata.com/static/open-graph/image.jpg",
    ],
    site: "https://whee.ai",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  userScalable: false,
};

const poppins = Poppins({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-poppins",
});

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  return (
    <html lang={locale} className={`${poppins.className} dark`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <meta
          name="google-site-verification"
          content="upC765SrGxvKdqJi66YQBTWVWk27sI7lZYeHit5s8P8"
        />
        <GTMInit />
      </head>
      <body>
        <GTMNoScript />
        <I18nProviderClient locale={locale}>
          <StoreProvider>
            <InitClient />
            <GlobalRouteTracker />
            <AntdRegistry>
              <SubscribeModalProvider>
                <QuestionProvider>{children}</QuestionProvider>
                <ModalStoreConsumer />
                <SubscribeModal />
              </SubscribeModalProvider>
            </AntdRegistry>
          </StoreProvider>
        </I18nProviderClient>
      </body>
    </html>
  );
}
