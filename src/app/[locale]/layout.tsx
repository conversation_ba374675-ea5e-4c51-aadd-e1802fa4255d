import type { Metada<PERSON> } from "next";
import type { Viewport } from "next";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { InitClient } from "@/components/InitClient";
import { GlobalRouteTracker } from "@/components/GlobalRouteTracker";
import "./global.css";
import { StoreProvider } from "@/contexts/StoreContext";
import AntdConfigProvider from "@/components/AntdConfigProvider";
import { I18nProviderClient } from "../../locales/client";
import { SubscribeModalProvider } from "@/contexts/SubscribeModalContext";
import { QuestionProvider } from "@/contexts/QuestionContext";
import { SubscribeModal } from "@/components/SubscribeModal";
import { ModalStoreConsumer } from "@/stores/ModalStore";
import { Tracer } from "@/components/Tracer";
import "@meitu/subscribe-intl/dist/style.css";
// import '@meitu/subscribe-intl/dist/themes/dark.css';
import "@/styles/dark.css";
import { Poppins } from "next/font/google";
import { GTMInit, GTMNoScript } from "@/components/GtmScript";
// import './global.scss';
export const metadata: Metadata = {
  title: {
    default: "WHEE - 高品质的AI素材生成器", // 默认 title（如果某个页面没设置 title）
    template: "WHEE - %s", // 页面自定义 title 时使用该模板
  },
  description:
    "WHEE是一款AI绘画与图片生成器，提供一站式AI视觉创作服务。WHEE不仅会画也会修图，各种AI修图功能一应俱全。使用门槛低，用户只需用自然语言表述需求，就能轻松上手。在画廊中，用户可以欣赏并学习来自多领域创作者的精美作品，为创作提供丰富的灵感来源，进而促进二创和设计师间的交流与合作。",
  openGraph: {
    title: "WHEE - 高品质的AI素材生成器",
    description:
      "WHEE是一款AI绘画与图片生成器，提供一站式AI视觉创作服务。WHEE不仅会画也会修图，各种AI修图功能一应俱全。使用门槛低，用户只需用自然语言表述需求，就能轻松上手。在画廊中，用户可以欣赏并学习来自多领域创作者的精美作品，为创作提供丰富的灵感来源，进而促进二创和设计师间的交流与合作。",
    url: "https://www.whee.com/",
    siteName: "WHEE",
    images: [
      {
        url: "https://titan-h5.meitu.com/whee/assets/qrCode/<EMAIL>",
        width: 1200,
        height: 630,
        alt: "WHEE - 高品质的AI素材生成器",
        type: "image/jpeg",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "WHEE - 高品质的AI素材生成器",
    description:
      "WHEE是一款AI绘画与图片生成器，提供一站式AI视觉创作服务。WHEE不仅会画也会修图，各种AI修图功能一应俱全。使用门槛低，用户只需用自然语言表述需求，就能轻松上手。在画廊中，用户可以欣赏并学习来自多领域创作者的精美作品，为创作提供丰富的灵感来源，进而促进二创和设计师间的交流与合作。",
    images: ["https://titan-h5.meitu.com/whee/assets/qrCode/<EMAIL>"],
    site: "https://www.whee.com/",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  userScalable: false,
};

const poppins = Poppins({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-poppins",
});

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  return (
    <html lang={locale} className={`${poppins.className} dark`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <meta
          name="google-site-verification"
          content="upC765SrGxvKdqJi66YQBTWVWk27sI7lZYeHit5s8P8"
        />
        <GTMInit />
      </head>
      <body>
        <GTMNoScript />
        <I18nProviderClient locale={locale}>
          <StoreProvider>
            <InitClient />
            <GlobalRouteTracker />
            <AntdRegistry>
              <SubscribeModalProvider>
                <QuestionProvider>{children}</QuestionProvider>
                <ModalStoreConsumer />
                <SubscribeModal />
              </SubscribeModalProvider>
            </AntdRegistry>
          </StoreProvider>
        </I18nProviderClient>
      </body>
    </html>
  );
}
