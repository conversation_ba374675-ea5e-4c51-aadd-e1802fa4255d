// worker.ts
import workerPool from 'workerpool';

//#region 类型声明与工具函数
type Pixel = {
  r: number;
  g: number;
  b: number;
  a: number;
};

function getPixel(
  image: ImageData,
  x: number,
  y: number,
  normalized = false
): Pixel {
  const col = Math.round(normalized ? x * image.width : x);
  const row = Math.round(normalized ? y * image.height : y);

  const offset = (row * image.width + col) * 4;
  return {
    r: image.data[offset],
    g: image.data[offset + 1],
    b: image.data[offset + 2],
    a: image.data[offset + 3]
  };
}

function putPixel(image: ImageData, x: number, y: number, pixel: Pixel) {
  const offset = (y * image.width + x) * 4;
  image.data[offset] = pixel.r;
  image.data[offset + 1] = pixel.g;
  image.data[offset + 2] = pixel.b;
  image.data[offset + 3] = pixel.a;
}

type ForEachPixelCallBackParams = Readonly<{
  images: ImageData[];
  normalizedPos: { x: number; y: number };
}>;

function forEachPixel(
  result: ImageData,
  images: ImageData[],
  callback: (params: ForEachPixelCallBackParams) => Pixel
) {
  const run = (pos: { x: number; y: number }) => {
    const normalizedPos = { x: pos.x / result.width, y: pos.y / result.height };
    return callback({ images, normalizedPos });
  };

  // const resolution = { width: result.width, height: result.height };
  for (let y = 0; y < result.height; ++y) {
    for (let x = 0; x < result.width; ++x) {
      const p1 = run({ x, y });
      const p2 = run({ x: x + 0.5, y });
      const p3 = run({ x, y: y + 0.5 });
      const p4 = run({ x: x + 0.5, y: y + 0.5 });

      const p = {
        r: (p1.r + p2.r + p3.r + p4.r) / 4,
        g: (p1.g + p2.g + p3.g + p4.g) / 4,
        b: (p1.b + p2.b + p3.b + p4.b) / 4,
        a: (p1.a + p2.a + p3.a + p4.a) / 4
      };
      putPixel(result, x, y, p);
    }
  }
}
//#endregion

//#region 超清替换alpha通道
function upscaleReplaceAlphaEachPixel({
  images,
  normalizedPos
}: ForEachPixelCallBackParams): Pixel {
  const hdImage = images[0];
  const originImage = images[1];

  const hdPixel = getPixel(hdImage, normalizedPos.x, normalizedPos.y, true);
  const originPixel = getPixel(
    originImage,
    normalizedPos.x,
    normalizedPos.y,
    true
  );

  return {
    ...hdPixel,
    a: originPixel.a
  };
}

function upscaleReplaceAlpha(hdImage: ImageData, originImage: ImageData) {
  const resultImage = new ImageData(hdImage.width, hdImage.height);
  forEachPixel(
    resultImage,
    [hdImage, originImage],
    upscaleReplaceAlphaEachPixel
  );
  return resultImage;
}
//#endregion

//#region 使用mask图抠图
function cutoutWithMaskEachPixel({
  images,
  normalizedPos
}: ForEachPixelCallBackParams): Pixel {
  const initImage = images[0];
  const maskImage = images[1];

  const initPixel = getPixel(initImage, normalizedPos.x, normalizedPos.y, true);
  const maskPixel = getPixel(maskImage, normalizedPos.x, normalizedPos.y, true);

  return {
    ...initPixel,
    a: initPixel.a * maskPixel.r
  };
}

function cutoutWithMask(initImage: ImageData, maskImage: ImageData) {
  const resultImage = new ImageData(initImage.width, initImage.height);
  forEachPixel(resultImage, [initImage, maskImage], cutoutWithMaskEachPixel);
  return resultImage;
}
//#endregion

//#region 判断是否有透明通道
function hasAlpha(imageData: ImageData) {
  const data = imageData.data;
  for (let i = 3; i < data.length; i += 4) {
    if (data[i] < 255) {
      return true;
    }
  }

  return false;
}
//#endregion

// create a worker and register public functions
workerPool.worker({
  hasAlpha,
  upscaleReplaceAlpha,
  cutoutWithMask
});
