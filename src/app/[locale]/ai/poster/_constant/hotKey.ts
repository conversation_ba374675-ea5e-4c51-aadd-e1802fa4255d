import { isWindows } from '@/utils/getOperationSystem';

export enum HotKey {
  copy = 'copy',
  paste = 'paste',
  cut = 'cut',
  front = 'front',
  back = 'back',
  delete = 'delete',
  merge = 'merge',
  split = 'split'
}

export const windowsHotKeyMap = {
  [HotKey.copy]: 'Ctrl C',
  [HotKey.paste]: 'Ctrl V',
  [HotKey.cut]: 'Ctrl X',
  [HotKey.front]: ']',
  [HotKey.back]: '[',
  [HotKey.delete]: '⌫',
  [HotKey.merge]: 'Ctrl G',
  [HotKey.split]: 'Ctrl ⌫'
};

export const macHotKeyMap = {
  [HotKey.copy]: 'Command C',
  [HotKey.paste]: 'Command V',
  [HotKey.cut]: 'Command X',
  [HotKey.front]: ']',
  [HotKey.back]: '[',
  [HotKey.delete]: '⌫',
  [HotKey.merge]: 'Command G',
  [HotKey.split]: 'Command ⌫'
};

export const getHotKeyMap = () => {
  return isWindows() ? windowsHotKeyMap : macHotKeyMap;
};
