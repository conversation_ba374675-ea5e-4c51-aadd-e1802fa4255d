export namespace Track {
  export enum FunctionEnum {
    TextToImage = "text_to_image",
    ImageToImage = "image_to_image",
    TextToPoster = "text_to_poster",
    ImageToPoster = "image_to_poster",
    /**
     * 超清
     */
    ImageEnhancer = "image_enhancer",
    /**
     * 抠图
     */
    BgRemover = "background_remover",
    /**
     * 消除
     */
    ObjectRemover = "object_remover",

    ModifyImage = "change_plan",

    ModifyText  = "correct_characters",
  }

  export enum BoardInfo {
    /** 无画板 */
    NoFrame = "0",
    /** 画板中有文字和图片 */
    TextAndImage = "1",
    /** 画板中仅有图片 */
    OnlyImage = "2",
    /** 画板中仅有文字 */
    OnlyText  = "3",
    /** 空画板 */
    Empty = "4",
  }

  export enum IsPictureUpload {
    Upload = "upload",
    Create = "create",
  }

  export enum CreditBalanceSufficient {
    NotEnough = "0",
    Enough = "1",
    Free  = "2",
  }
}