"use client";
import { RootStoreProvider, useRootStore } from "./_store";
import { GuideCreateProjectContextProvider } from "./_context/GuideCreateProjectContext";
import { FormContextProvider } from "./_context/FormContext";
import Header from "./_components/Header";
import styles from "./index.module.scss";
import UploadLoading from "./_components/UploadLoading";
import EditorStructure from "./_EditorStructure";
import { observer } from "mobx-react-lite";
import { useStore } from "@/contexts/StoreContext";
import { Fragment, useEffect } from "react";
import { AIPoster } from "@/api/types/aiPoster/task";
import ParamsSidebarContextProvider from "./_components/ParamsSidebar/context"
import dynamic from "next/dynamic";

const OnlineConsultationModal = dynamic(
  () => import("@/components/OnlineConsultationModal"),
  { ssr: false }
);

function EditorLayout({ children }: { children: React.ReactNode }) {
  return (
    <RootStoreProvider>
      <GuideCreateProjectContextProvider>
        <FormContextProvider>
          <EditorRoot>{children}</EditorRoot>
        </FormContextProvider>
      </GuideCreateProjectContextProvider>
    </RootStoreProvider>
  );
}

type EditorRootProps = React.PropsWithChildren;
function EditorRoot({ children }: EditorRootProps) {
  const { userStore } = useStore();
  const { pollingManagers, renderStore, selectionStore } = useRootStore();

  // 任务失败后刷新美豆余额
  useEffect(() => {
    const updateMtBalanceAtTaskFailed = (payload: {
      resolved: AIPoster.Task[];
    }) => {
      const failedTasks = payload.resolved.filter((task) => {
        // 任务状态为失败的任务
        if (task.loadingStatus !== AIPoster.LoadingStatus.Success) {
          return true;
        }

        // 任务状态为成功 但是所有结果图都没有过审核
        const successImages = task.resultImages.filter(
          (result) => result.imageStatus === AIPoster.ImageStatus.Success
        );
        return successImages.length === 0;
      });

      if (failedTasks.length) {
        userStore.refreshMtBeanBalance();
      }
    };

    pollingManagers.tasksPollingManager.addTaskResolvedListener(
      updateMtBalanceAtTaskFailed
    );

    return () => {
      pollingManagers.tasksPollingManager.removeTaskResolvedListener(
        updateMtBalanceAtTaskFailed
      );
    };
  }, [pollingManagers.tasksPollingManager]);

  return (
    <ParamsSidebarContextProvider
      selectionStore={selectionStore}
      renderStore={renderStore}
    >
      <Header />
      <main className={styles.editorMainBox}>
        <EditorStructure>{children}</EditorStructure>
      </main>
      <UploadLoading />
      <OnlineConsultationModal />
    </ParamsSidebarContextProvider>
  );
}

export default observer(EditorLayout);
