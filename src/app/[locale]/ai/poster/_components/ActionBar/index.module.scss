@import '@/styles/variable.scss';
@import '../zIndex.scss';
.action-bar {
  box-sizing: border-box;
  width: 178px;
  height: 44px;
  border: 1px solid #e2e8f0;
  padding: 4px;
  z-index: $z-index-action-bar;

  position: fixed;
  right: 64px;
  bottom: 16px;

  display: flex;
  align-items: center;

  border-radius: var(--radius-12, 12px);
  border: 1px solid $system-stroke-input;
  background: $system-background-sixth;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 6px 24px 0px rgba(0, 0, 0, 0.30);
  user-select: none;

  // &::after {
  //   content: '';
  //   display: block;
  //   width: 2px;
  //   height: 26px;
  //   background: $system-stroke-button;
  //   position: absolute;
  //   right: 42px;
  //   top: 50%;
  //   transform: translateY(-50%);
  // }

  :global {
    .action-button {
      width: 36px;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 0 0 auto;
      border-radius: 8px;
      cursor: pointer;

      svg {
        width: 16px;
        height: 16px;
        color: $system-content-secondary;
      }
    
      &:hover:not(:global(.disabled)):not(:global(.selected)) {
        background: $system-background-seventh;
        // box-shadow: 0px 0px 0px 1px $system-stroke-button inset;
    
        // svg {
        //   color: #fff;
        // }
      }
    
      &:global(.selected) {
        background: $system-content-brand-primary;
    
        svg {
          color: #fff;
        }
      }
    
      &:global(.disabled) {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &.focus {
        margin-left: 4px;
      }
    }
  }
}

.zoom-input {
  width: 54px !important;
  margin-left: 4px;
  background-color: $system-background-sixth !important;
  border: 1px solid $system-stroke-input !important;
  font-size: 12px !important;
  padding-left: 4px !important;
  color: $system-content-brand-thirdary !important;

  &:hover {
    background-color: $system-background-sixth;
    border: 1px solid $system-stroke-input;
  }
  
  input {
    font-size: 12px !important;
    color: $system-content-brand-thirdary !important;
  }

  &:focus-within,
  &:focus,
  &:focus-visible {
    box-shadow: none;
    background-color: $system-background-sixth;
    border: 1px solid $system-stroke-input;
  }

  & > span {
    margin-inline-end: 4px !important;
  }
}
