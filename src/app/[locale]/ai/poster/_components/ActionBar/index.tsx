import { observer } from "mobx-react";
import { AimBold, RedoBold, UndoBold } from "@meitu/candy-icons";
import classNames from "classnames";
import Zoom from "./Zoom";
import { useRootStore } from "@/app/[locale]/ai/poster/_store";
import { useEffect, useState } from "react";
import { Tooltip } from "antd";
import styles from "./index.module.scss";
import { useI18n } from "@/locales/client";

function ActionBar() {
  const t = useI18n();
  const [hasUndo, setHasUndo] = useState(false);
  const { renderStore, editorStatusStore } = useRootStore();

  const [historyStatus, setHistoryStatus] = useState({
    redoEnabled: true,
    undoEnabled: true,
  });

  useEffect(() => {
    const render = renderStore.render;
    const historyPlugin = renderStore.historyPlugins;
    if (!render || !historyPlugin) {
      return;
    }

    const handleHistoryChange = () => {
      const undoEnabled = !!historyPlugin.history.undoStack.length;
      const redoEnabled = !!historyPlugin.history.redoStack.length;

      setHistoryStatus({
        undoEnabled,
        redoEnabled,
      });
    };

    handleHistoryChange();
    render._FC.on("history:changed", handleHistoryChange);
    return () => {
      render._FC.off("history:changed", handleHistoryChange);
    };
  }, [renderStore.render, renderStore.historyPlugins]);

  const render = renderStore.render;
  const historyPlugin = renderStore.historyPlugins;

  if (!render || !historyPlugin) {
    return null;
  }

  const handleClickFocus = () => {
    renderStore.render?.backToOriginPosition();
  };

  return !editorStatusStore.isClearScreenUI ? (
    <div className={styles["action-bar"]}>
      <Tooltip
        title={
          !historyStatus.undoEnabled || hasUndo
            ? ""
            : t(
                "If you cancel a task that is in progress, the credits will still be deducted, and the results of the task will appear in the history section."
              )
        }
      >
        <div
          className={classNames(
            "action-button",
            !historyStatus.undoEnabled && "disabled"
          )}
          onClick={() => {
            if (!historyStatus.undoEnabled) {
              return;
            }
            // 点击过回退，不再Tooltip
            setHasUndo(true);

            historyPlugin.undo();
          }}
        >
          <UndoBold />
        </div>
      </Tooltip>
      <div
        className={classNames(
          "action-button",
          !historyStatus.redoEnabled && "disabled"
        )}
        onClick={() => {
          if (!historyStatus.redoEnabled) {
            return;
          }

          historyPlugin.redo();
        }}
      >
        <RedoBold />
      </div>
      <Zoom />
      <div
        className={classNames("action-button focus")}
        onClick={handleClickFocus}
      >
        <AimBold />
      </div>
    </div>
  ) : null;
}

export default observer(ActionBar);
