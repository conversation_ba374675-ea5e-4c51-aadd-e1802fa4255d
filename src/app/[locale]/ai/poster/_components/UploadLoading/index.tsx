'use client';

import { useI18n } from "@/locales/client";
import { message } from "antd";
import { observer } from "mobx-react-lite";
import { useEffect, useRef } from "react";
import { useRootStore } from "@/app/[locale]/ai/poster/_store";
import toast from "@/components/Toast";

const uploadingMessageKey = 'message-key/uploading'

function UploadLoading() {
  
  const t = useI18n();
  const { editorStatusStore } = useRootStore();
  
  const isUploadingShow = useRef(false);
  const timeoutKey = useRef(0);
  
  useEffect(() => {
    if (editorStatusStore.isUploading) {
      if (isUploadingShow.current) {
        return;
      }

      timeoutKey.current = window.setTimeout(() => {
        toast.show({
          type: "loading",
          title: t('Uploading...'),
          description: '',
          duration: 0,
          key: uploadingMessageKey,
        });
        isUploadingShow.current = true;
        timeoutKey.current = 0;
      }, 200);
    } else {
      if (isUploadingShow.current) {
        toast.destroy(uploadingMessageKey);
        isUploadingShow.current = false;
      }

      if (timeoutKey.current) {
        window.clearTimeout(timeoutKey.current);
        timeoutKey.current = 0;
      }
    }
  }, [editorStatusStore.isUploading]);

  useEffect(() => {
    return () => {
      if (isUploadingShow.current) {
        toast.destroy(uploadingMessageKey);
        isUploadingShow.current = false;
      }

      if (timeoutKey.current) {
        window.clearTimeout(timeoutKey.current);
        timeoutKey.current = 0;
      }
    }
  }, []);

  return null;
}

export default observer(UploadLoading);