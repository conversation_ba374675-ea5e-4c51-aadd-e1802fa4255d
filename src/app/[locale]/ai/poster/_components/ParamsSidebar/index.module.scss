@import "@/styles/variable.scss";
@import "../zIndex.scss";

$bottom-button-height: 120px;

.sidebarWrapper {
  position: absolute;
  left: 0;
  top: 0;
  z-index: $z-index-params-drawer;
  width: 320px;
  height: 100%;
  transition: transform 0.3s ease-in-out;
  transform: translate(-320px);

  &:global(.open) {
    transform: translate(0);

    :global(.drawer-trigger-icon) {
      transform: rotate(180deg);
    }
  }
}

.sidebar {
  position: absolute;
  left: 0;
  top: 0;
  z-index: $z-index-params-drawer;
  width: 320px;
  height: 100%;
  background: $system-background-secondary;
  

  :global {

    .sidebar-form {
      // padding-bottom: $bottom-button-height;
      height: calc(100vh - 47px - $bottom-button-height);
      overflow: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .sidebar-section {
      box-sizing: border-box;
      width: 320px;
      padding: 16px;
      background: $background-input;
      margin-bottom: 6px;
      color: $system-content-secondary;

      &-title {
        font-size: 14px;
        height: 32px;
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }
    }

    .meidou-button {
      position: fixed;
      width: 320px;
      left: 0;
      bottom: 0;
    }
  }
}

.drawerTrigger {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 336px;
  top: 50%;
  transform: translate(-100%, -50%);
  width: 16px;
  height: 72px;
  border-radius: 0 8px 8px 0;
  background: #fff;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
    0px 6px 24px 0px rgba(0, 0, 0, 0.06);
  cursor: pointer;

  :global(.drawer-trigger-icon) {
    svg {
      width: 14px;
      height: 14px;
      color: $system-content-thirdary;
    }
  }
}