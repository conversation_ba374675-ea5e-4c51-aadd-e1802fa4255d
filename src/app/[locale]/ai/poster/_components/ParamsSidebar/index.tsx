import { Form } from "antd";
import { observer } from "mobx-react-lite";
import styles from "./index.module.scss";
import Prompt, { TemplateName } from "./Prompt";
import ImageSection from "./ImageSection";
import { useRootStore } from "../../_store";
import { useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";
import BaseModelSection from "./BaseModel";
import { useBaseModelList } from "./useBaseModelList";
import Ratio from "./Ratio";
import BatchSize from "./BatchSize";
import { AIPoster, ImageParamsForm, Submit } from "@/api/types/aiPoster/task";
import {
  createImage,
  FabricObject,
  getElementOptions,
  Group,
  LoadingType,
  WarningType,
} from "@meitu/whee-infinite-canvas";
import { submitTask } from "@/api/aiPoster/task";
import { MtccFuncCode } from "@/types";
import { handleSubmitError } from "../../_utils/submit";
import { useStore } from "@/contexts/StoreContext";
import { dispatch } from "../../_utils/TaskDispatcher";
import { useI18n } from "@/locales/client";
import { useGuideCreateProject } from "../../_context/GuideCreateProjectContext";
import toast from "@/components/Toast";
import MeiDouButton from "@/components/MeiDou";
import { FunctionCode } from "@/api/types/meidou";
import { trackEvent } from "@/services/tracer";
import { Track } from "../../_constant/track";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import useSizeExceedConfirmModal from "../../_hooks/useSizeExceedConfirmModal";
import { useParamsSidebarContext, useParamsSidebarContextRegister } from "./context";
import { HardCodeModelFlag } from "./hard-code-model-flag";
import { getRatioSize } from "./getRatioSize";
import { ChevronRightNarrowBlack } from "@meitu/candy-icons";
import { getImg2ImgFlag, useImg2ImgFlag } from "./context/img2img-mark";
import classNames from "classnames";
import { placeholderImageUrl } from "../../_constant/placeholderImage";

function ParamsSidebar() {
  const rootStore = useRootStore();
  const { selectionStore, editorStatusStore, renderStore, projectsStore } =
    rootStore;
  const { userStore } = useStore();
  const { render, historyPlugins } = renderStore;
  const singleImage = selectionStore.singleImage;
  const imageOptions = singleImage?.options;
  const selectedImageSrc = imageOptions?.src;
  const t = useI18n();
  
  const [form] = Form.useForm();
  const register = useParamsSidebarContextRegister();
  useLayoutEffect(() => {
    register.form(form);
  }, [])



  const { baseModelList, baseModelLoading, validateImageEdit, openImageEditSwitch, changeBaseModelId, closeImageEditSwitch } = useParamsSidebarContext();
  const imageEdit = Form.useWatch("imageChecked", form);
  // const { open: openSubscribeModal } = useSubscribeModal();
  const { createGuideProject, isInGuidePage } = useGuideCreateProject();
  const { enableImg2Img } = useImg2ImgFlag({ renderStore, projectsStore });
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();
  const baseModelId = Form.useWatch("baseModelId", form);

  const activeBaseModel = useMemo(() => {
    return baseModelList.find((item) => item.id === baseModelId);
  }, [baseModelList, baseModelId]);

  /**
   * 文生图不能选通用编辑模型
   * 图生图不能选通用生成模型
   */
  const disabledBaseModel = useMemo(() => {
    if (imageEdit) {
      const model = baseModelList.find(
        (model) => model.modelFlag === HardCodeModelFlag.CommonGenerate
      );
      if (model) {
        return [model.id];
      }

      return [];
    }

    const models = baseModelList.filter(
      (model) => model.modelFlag === HardCodeModelFlag.CommonEdit || model.modelFlag === HardCodeModelFlag.ProfessionalEdit
    ).map(m => m.id);
    return models ?? [];
  }, [imageEdit, baseModelList]);


  const elementRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    editorStatusStore.paramsPanelContainer = elementRef.current;
  });

  const ratioList = useMemo(() => {
    if (!activeBaseModel) {
      return [];
    }

    return getRatioSize(activeBaseModel, imageEdit);
  }, [activeBaseModel, imageEdit]);

  const batchSize = Form.useWatch("batchSize", form);

  /**
   * 拉取到服务端的模型列表后，如果当前没有选中的模版
   * 1. 默认选中“通用生图模型”。若没有，则
   * 2. 选中第一个模型
   */
  useEffect(() => {
    if (!baseModelList.length || form.getFieldsValue().baseModelId) {
      return;
    }

    const model =
      baseModelList.find(
        (item) => item.modelFlag === HardCodeModelFlag.CommonGenerate
      ) || baseModelList[0];

    if (!model) {
      return;
    }

    changeBaseModelId(model.id, false);
  }, [baseModelList, baseModelId, form]);

  /**
   * 用来记录用户是否修改了提示词
   * 如果没有修改提示词，在点击文字模版时，执行替换而不是追加
   */
  const userChangePrompt = useRef(false);

  useEffect(() => {
    if (!render) {
      return;
    }

    // 如果选中的图片有tag但是没有通过验证 也要关闭图生图开关
    if (singleImage?.image && getImg2ImgFlag(singleImage.image, render) && !validateImageEdit(singleImage.image)) {
      closeImageEditSwitch();
      return;
    }

    const params = singleImage?.options._custom_data_history_?.params;
    userChangePrompt.current = false;

    // 如果是文生图/图生图的结果
    if (params && params.baseModelId) {
      const hasInitImage = !!params.initImages?.[0];

      // 如果是图生图 回填生图参数 并将结果图作为参考图
      if (hasInitImage) {
        form.setFieldsValue({
          prompt: params.prompt,
          baseModelId: params.baseModelId,
          imageChecked: true,
          image: selectedImageSrc,
          ratio: params.picRatio,
          batchSize: params.batchSize,
        });

        return;
      } else {
        // 如果是文生图 切没有开启过图生图 回填文生图参数
        if (!getImg2ImgFlag(singleImage.image, render)) {
          form.setFieldsValue({
            prompt: params.prompt,
            baseModelId: params.baseModelId,
            imageChecked: false,
            ratio: params.picRatio,
            image: '',
            batchSize: params.batchSize,
          });
          return;
        }

        // 如果是文生图 且开启过图生图 则打开图生图的参数
      }
    }

    if (selectedImageSrc && singleImage?.image && getImg2ImgFlag(singleImage.image, render)) {
      openImageEditSwitch({
        shape: singleImage.image
      });
      return;
    }

    closeImageEditSwitch();

  }, [singleImage?.image, selectedImageSrc, singleImage?.options._custom_data_history_?.params]);

  const handleSelectPromptTemplate = (template: TemplateName) => {
    if (
      template === "改对象" ||
      template === "改风格" ||
      template === "改背景"
    ) {
      openImageEditSwitch({ flag: HardCodeModelFlag.ProfessionalEdit });
    }

    if (template === "改文字") {
      openImageEditSwitch({ flag: HardCodeModelFlag.CommonEdit });
    }

    if (singleImage?.image) {
      enableImg2Img(singleImage.image);
    }
  };

  //#region 商业化
  const functionBody = useMemo(() => {
    return JSON.stringify({
      batch_size: batchSize,
      base_model_id: baseModelId,
    });
  }, [batchSize, baseModelId]);
  //#endregion

  //#region 任务提交
  const [submitLoading, setSubmitLoading] = useState(false);
  const decomposePicRatio = (ratio: string) => {
    const exist =
      activeBaseModel &&
      getRatioSize(activeBaseModel, imageEdit).find((r) => r.ratio === ratio);
    return {
      width: exist?.width ?? 0,
      height: exist?.height ?? 0,
      picRatio: exist?.ratio ?? "self-adaption",
    };
  };
  // 将表单数据转换为请求参数
  const toImage2ImageQueryParams = (
    values: ImageParamsForm
  ): Submit.Image2ImageParams => {
    const activeObject = singleImage?.image;

    if (!activeObject) {
      // eslint-disable-next-line no-throw-literal
      throw "没有选中图片，触发了图生图";
    }

    const activeOptions =
      render && getElementOptions.call(render, activeObject);

    return {
      prompt: values.prompt,
      baseModelId: values.baseModelId,
      batchSize: values.batchSize,
      ...decomposePicRatio(values.ratio),
      initImages: [
        activeOptions?._custom_data_history_?.urlShort ?? values.image,
      ],
      linkMsgId: activeOptions?._custom_data_history_?.msgId,
      initWatermarkImage: values.image ?? "",
    };
  };

  const toText2ImageQueryParams = (
    values: ImageParamsForm
  ): Submit.Text2ImageParams => {
    return {
      prompt: values.prompt,
      baseModelId: values.baseModelId,
      batchSize: values.batchSize,
      ...decomposePicRatio(values.ratio),
    };
  };

  // 创建图生图
  const createImageToImage = async (values: ImageParamsForm) => {
    const activeObject = singleImage?.image;

    if (!activeObject) {
      return;
    }

    // 图形loading结束
    const imageToImageLoaded = () => {
      render?.Actions.setLoaded(activeObject?._id_ || "");
    };

    const imageToImageLoading = () => {
      activeObject?._id_ &&
        render?.Actions.setLoading({
          type: LoadingType.CIRCLE_DANCE,
          blur: true,
          maskOpacity: 0.2,
          text: t("Generating..."),
          id: activeObject._id_,
        });
    };

    let msgId = "";
    const projectId = projectsStore.activeProjectId;
    if (!projectId || !render || !historyPlugins) {
      return;
    }
    // 图形loading
    const operation = historyPlugins.baseAction.getLoadOperation([
      activeObject,
    ]);

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== activeObject
      ) {
        return;
      }

      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);
    operation && historyPlugins.submit(operation);

    setSubmitLoading(true);
    // 图形loading
    imageToImageLoading();

    const params = toImage2ImageQueryParams(values);

    try {
      const res = await submitTask({
        projectId,
        // TODO
        // layerId: activeObject?._id_,
        taskCategory: AIPoster.TaskCategory.Image2Image,
        params: toImage2ImageQueryParams(values),
        functionName: MtccFuncCode.FuncCodePosterImage2Image,
      });
      setSubmitLoading(false);
      editorStatusStore.addActiveTask(res.id);
      msgId = res.id;
    } catch (err) {
      handleSubmitError(err, {
        userStore: userStore,
        openMeiDouRecordsPopup,
      });
      imageToImageLoaded();
      setSubmitLoading(false);
      return;
    } finally {
      // 更新美豆
      userStore.refreshMtBeanBalance();

      // restore();
    }

    // 如果任务创建失败 或者任务被中断 不继续执行
    if (!msgId || abortController.signal.aborted) {
      return;
    }

    return dispatch({
      msgId,
      rootStore,
      shape: activeObject,
      abortController,
      expandCustomData: {
        params,
      },
      t,
    }).finally(() => {
      // 更新美豆
      userStore.refreshMtBeanBalance();
    });
  };

  // 文生图
  const createTextToImage = async (values: ImageParamsForm) => {
    const { width, height, picRatio } = decomposePicRatio(values.ratio);
    // 画布loading
    const startLoading = async () => {
      if (!render) return;
      // 插入占位图片
      const insertedImageEle = await createImage("", {
        src: placeholderImageUrl,
        width: width,
        height: height,
      });

      render.addToViewPortCenterByArrangement(insertedImageEle);
      render._FC.setActiveObject(insertedImageEle);
      render.backToOriginPosition({ target: insertedImageEle });
      render._FC.requestRenderAll();

      // 给插入的元素设置生成参数
      insertedImageEle?.set({
        _custom_data_history_: {
          params: toText2ImageQueryParams(values),
          imageStatus: AIPoster.ImageStatus.AuditFailed,
        },
      });

      if (!historyPlugins) return;

      const addOperation = historyPlugins.baseAction.getAddOperation({
        objects: [insertedImageEle],
      });

      const loadingOperation = historyPlugins.baseAction.getLoadOperation([
        insertedImageEle as FabricObject,
      ]);

      addOperation && historyPlugins.submit(addOperation);
      loadingOperation && historyPlugins.submit(loadingOperation);

      // 设置图片loading
      insertedImageEle?._id_ &&
        render?.Actions.setLoading({
          type: LoadingType.CIRCLE_DANCE,
          text: t("Generating..."),
          id: insertedImageEle._id_,
        });

      return insertedImageEle;
    };

    let msgId = "";
    let insertedImageEle = {} as Group | undefined;
    const projectId = projectsStore.activeProjectId;
    if (!projectId || !render || !historyPlugins) {
      return;
    }

    setSubmitLoading(true);

    // 画布结束loading
    const loadingEnd = () => {
      render?.Actions.setLoaded(insertedImageEle?._id_ || "");
    };

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== insertedImageEle
      ) {
        return;
      }

      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);

    try {
      const res = await submitTask({
        projectId,
        // layerId: insertedImageEle?._id_,
        taskCategory: AIPoster.TaskCategory.Text2Image,
        // 生图参数最长边1024
        params: toText2ImageQueryParams(values),
        functionName: MtccFuncCode.FuncCodePosterText2Image,
      });
      editorStatusStore.addActiveTask(res.id);
      setSubmitLoading(false);

      // 图片占位元素
      insertedImageEle = await startLoading();

      msgId = res.id;
    } catch (err) {
      handleSubmitError(err, {
        userStore: userStore,
        openMeiDouRecordsPopup,
      });
      loadingEnd();
      setSubmitLoading(false);
      return;
    } finally {
      // 更新美豆
      userStore.refreshMtBeanBalance();
    }

    // 如果任务创建失败 或者任务被中断 不继续执行
    if (!msgId || abortController.signal.aborted) {
      return;
    }

    return dispatch({
      msgId,
      rootStore,
      shape: insertedImageEle,
      abortController,
      t,
    }).finally(() => {
      // 更新美豆
      userStore.refreshMtBeanBalance();
    });
  };

  const onConfirm = async () => {
    if (!projectsStore.activeProjectId) {
      if (!isInGuidePage) {
        return;
      }

      await createGuideProject();

      if (!projectsStore.activeProjectId) {
        return;
      }
    }

    form.getFieldValue("imageChecked")
      ? createImageToImage(form.getFieldsValue())
      : createTextToImage(form.getFieldsValue());
  };

  const {
    open: openImageToImageModal,
    contextHolder: imageToImageContextHolder,
    getNeedsOpen: getImageToImageModalNeedsOpen,
  } = useSizeExceedConfirmModal({
    feature: AIPoster.TaskCategory.Image2Image,
    title: "确认要继续吗？",
    okText: "继续",
    cancelText: "取消",
    description:
      "由于图片尺寸过大会影响图像生成效果，请确认是否继续处理",
    onConfirm,
  });

  const handleSubmit = async (values: ImageParamsForm) => {
    if (!projectsStore.activeProjectId) {
      if (!isInGuidePage) {
        return;
      }

      await createGuideProject();
      if (!projectsStore.activeProjectId) {
        return;
      }
    }

    if (values.imageChecked) {
      if (!values.image) {
        toast.error(t("Upload an image to start editing."));
        return;
      }

      if (!values.prompt) {
        toast.error(t("Enter a prompt to generate content."));
        return;
      }

      const imageWidth = singleImage?.options.imageWidth || 0;
      const imageHeight = singleImage?.options.imageHeight || 0;

      const sizeLimitation = activeBaseModel?.defaultParams;
      if (sizeLimitation?.inputImageMinWidth && imageWidth < sizeLimitation.inputImageMinWidth
        || sizeLimitation?.inputImageMaxWidth && imageWidth > sizeLimitation.inputImageMaxWidth
        || sizeLimitation?.inputImageMinHeight && imageHeight < sizeLimitation.inputImageMinHeight
        || sizeLimitation?.inputImageMaxHeight && imageHeight > sizeLimitation.inputImageMaxHeight
      ) {
       
        if (getImageToImageModalNeedsOpen()) {
          return openImageToImageModal();
        }
      }

      return await createImageToImage(values);
    } else {
      if (!values.prompt) {
        toast.error(t("Enter a prompt to generate content."));
        return;
      }

      return await createTextToImage(values);
    }
  };

  const handleClickTrigger = () => {
    editorStatusStore.toggleLeftSider();
  };
  //#endregion

  return (
    <>
      <div className={classNames(styles.sidebarWrapper, {open: editorStatusStore.isLeftSiderOpen})}>
        <aside className={classNames(styles.sidebar)} ref={elementRef}>
          <Form
            form={form}
            className="sidebar-form"
            onKeyDown={(e) => e.nativeEvent.stopImmediatePropagation()}
            initialValues={{
              ratio: "3:4",
              batchSize: 4,
            }}
            onFinish={handleSubmit}
          >
            <div id="poster-params-section">
              <Form.Item noStyle name="prompt">
                <Prompt
                  userChangePrompt={userChangePrompt}
                  onSelectTemplate={handleSelectPromptTemplate}
                />
              </Form.Item>

              <ImageSection />

              <Form.Item noStyle name="baseModelId">
                <BaseModelSection
                  loading={baseModelLoading}
                  list={baseModelList}
                  disabledIdList={disabledBaseModel}
                  onChange={(id) => {
                    changeBaseModelId(id, imageEdit);
                  }}
                />
              </Form.Item>
            </div>

            <Form.Item noStyle name="ratio">
              <Ratio options={ratioList} imageEdit={imageEdit} />
            </Form.Item>

            <Form.Item noStyle name="batchSize">
              <BatchSize />
            </Form.Item>

            <MeiDouButton
              functionCode={
                imageEdit
                  ? FunctionCode.aiPosterImg2img
                  : FunctionCode.aiPosterText2img
              }
              functionId={
                imageEdit
                  ? MtccFuncCode.FuncCodeImage2Image
                  : MtccFuncCode.FuncCodeText2Image
              }
              fetchPriceLoading={submitLoading}
              functionBody={functionBody}
              // 生成中的禁止生成
              disabled={selectionStore.singleImage?.options._loading_}
              onClick={({ deficit, price }) => {
                const values = form.getFieldsValue();
                const activeObject = singleImage?.image;
                const options =
                  render &&
                  activeObject &&
                  getElementOptions.call(render, activeObject);
                /**
                 * 被选中的图片是生成的：
                 * 1. 图片有msgId
                 * 2. 是模版的预览图
                 */
                const imageIsCreate =
                  !!options?._custom_data_history_?.msgId ||
                  options?._custom_data_history_?.isFromTemplatePreview;

                const params = imageEdit
                  ? toImage2ImageQueryParams(values)
                  : toText2ImageQueryParams(values);

                trackEvent("create_btn_click", {
                  function: imageEdit
                    ? Track.FunctionEnum.ImageToImage
                    : Track.FunctionEnum.TextToImage,
                  board_info: Track.BoardInfo.NoFrame,
                  is_picture_upload: imageEdit
                    ? imageIsCreate
                      ? Track.IsPictureUpload.Create
                      : Track.IsPictureUpload.Upload
                    : "",
                  base_model_id: params.baseModelId,
                  prompt: params.prompt,
                  batch_size: params.batchSize,
                  free_batch_size: price?.useFreeNum,
                  ratio: params.picRatio,
                  width: params.width,
                  height: params.height,
                  is_vip: userStore.vipLevel,
                  // 消耗美豆为0 上报免费
                  credit_balance_sufficient:
                    price?.amount === 0
                      ? Track.CreditBalanceSufficient.Free
                      : // 小号不为0 判断是否足够
                      deficit
                      ? Track.CreditBalanceSufficient.NotEnough
                      : Track.CreditBalanceSufficient.Enough,
                });
              }}
            />
          </Form>
        </aside>
        <div className={classNames(styles.drawerTrigger, {open: editorStatusStore.isLeftSiderOpen})} onClick={handleClickTrigger}>
          <ChevronRightNarrowBlack className="drawer-trigger-icon" />
        </div>
        {imageToImageContextHolder}
      </div>
    </>
  );
}

export default observer(ParamsSidebar);
