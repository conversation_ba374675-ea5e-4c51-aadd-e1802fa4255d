import { Form, Radio } from "antd";
import { observer } from "mobx-react";
import classNames from "classnames";
import styles from './index.module.scss';

type BatchSizeProps = {
  value?: number;
  onChange?(newValue: number): void;
  options?: number[];
};

function BatchSize({
  value,
  onChange,
  options = [1, 2, 3, 4],
}: BatchSizeProps) {
  return (
    <section className={classNames("sidebar-section", styles.size)}>
      <div className="sidebar-section-title">
        <strong>生成张数</strong>
      </div>
      <Radio.Group className="gen-num" value={value} onChange={e => onChange?.(e.target.value)}>
        {options.map((number) => {
          return (
            <Radio key={number} className="gen-num-radio" value={number}>
              <div className="gen-num-radio-label">{number}</div>
            </Radio>
          );
        })}
      </Radio.Group>
    </section>
  );
}

export default observer(BatchSize);
