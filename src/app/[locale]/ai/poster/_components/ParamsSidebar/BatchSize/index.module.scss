@import "@/styles/variable.scss";

.size {
  :global {
    .sidebar-section-title {
      font-weight: 500;
    }

    .gen-num.ant-radio-group {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .gen-num-radio.ant-radio-wrapper {
        margin: 0;

        .ant-wave-target {
          display: none;

          &+span {
            padding: 0;
          }
        }

        .gen-num-radio-label {
          display: flex;
          width: 69px;
          height: 32px;
          justify-content: center;
          align-items: center;
          border-radius: var(--radius-8, 8px);
          box-shadow: 0 0 0 1px $system-stroke-input inset;
          background: $system-background-primary;
          color: #293545;
        }

        &.ant-radio-wrapper-checked {
          .gen-num-radio-label {
            box-shadow: 0 0 0 1.5px $system-content-brand-primary inset;
            background: rgba(53, 73, 255, 0.05);;
          }
        }
      }
    }
  }
}
