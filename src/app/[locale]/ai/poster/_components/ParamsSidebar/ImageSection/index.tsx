import { observer } from "mobx-react";
import styles from "./index.module.scss";
import { Form, Spin, Switch, Image } from "antd";
import { ChevronRightBlack, PlusBold, TrashCanBold } from "@meitu/candy-icons";
import { useI18n } from "@/locales/client";
import FilePicker from "@/components/FilePicker";
import { useUpload } from "@/app/[locale]/ai/poster/_hooks/useUpload";
import { useState } from "react";
import { toAtlasImageView2URL } from "@meitu/util";
import CardLoading from "@/components/Loading/card-loading";
import classNames from "classnames";
import { useMonitorUploadFunc } from "@/hooks/useMonitorUploadFunc";
import { useParamsSidebarContext } from "../context";
import { useRootStore } from "../../../_store";
import { useImg2ImgFlag } from "../context/img2img-mark";
import { useStore } from "@/contexts/StoreContext";
import { wheeLoginPopup } from "@/utils/account";

type ImageSectionProps = {};

const ImageSection = ({}: ImageSectionProps) => {
  const form = Form.useFormInstance();
  const t = useI18n();
  const { selectionStore, renderStore, projectsStore } = useRootStore();
  const { validateImageEdit, openImageEditSwitch, closeImageEditSwitch } =
    useParamsSidebarContext();

  const singleImage = selectionStore.singleImage;
  const { enableImg2Img, disableImg2Img } = useImg2ImgFlag({
    renderStore,
    projectsStore,
  });

  return (
    <section className={classNames(styles.image, "sidebar-section")}>
      <div className={classNames("sidebar-section-title", "image-title")}>
        <div className="image-title-content">
          <strong>{t("Image Variation")}</strong>
        </div>
        <Form.Item name={"imageChecked"} noStyle>
          <Switch
            className="image-title-switch"
            onChange={(open, e) => {
              // 如果选中了图片，验证通过才可以打开
              if (
                open &&
                singleImage &&
                !validateImageEdit(singleImage.image)
              ) {
                e.preventDefault();
                form.setFieldValue("imageChecked", false);
                return;
              }

              if (!open && singleImage) {
                disableImg2Img(singleImage.image);
              }

              if (open) {
                // 打开当前图形的图生图开关 并且记录可以开启图生图
                openImageEditSwitch({});
                if (singleImage?.image) {
                  enableImg2Img(singleImage.image);
                }
              } else {
                closeImageEditSwitch();
                if (singleImage?.image) {
                  disableImg2Img(singleImage.image);
                }
              }
            }}
          />
        </Form.Item>
      </div>

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.imageChecked !== currentValues.imageChecked ||
          prevValues.image !== currentValues.image
        }
      >
        {() => {
          return (
            form.getFieldsValue().imageChecked && (
              <Form.Item name={"image"} noStyle>
                <PosterUploader />
              </Form.Item>
            )
          );
        }}
      </Form.Item>
    </section>
  );
};

const PosterUploader = observer((props: {
  value?: string;
  onChange?: (value: string) => void;
}) => {
  const { value, onChange } = props;
  const t = useI18n();
  const uploadFunc = useMonitorUploadFunc({
    beforeValidator: {
      ratio: {
        wh: 3,
        hw: 3,
      },
      size: 10,
      dimension: {
        max: 4096,
        min: 50,
      },
    },
  });
  const upload = useUpload({
    upload: uploadFunc,
  });
  const [uploadLoading, setUploadLoading] = useState(false);
  const { userStore } = useStore();

  const handleFileChange = async (fileList: FileList | null) => {
    const file = fileList?.[0];
    if (!file) {
      return;
    }
    setUploadLoading(true);
    const shape = await upload(file, {
      extendsCustomDataHistory: { img2img: true },
    });
    setUploadLoading(false);
  };

  return (
    <>
      {value ? (
        <div className={styles.imageBox}>
          <div className={styles.leftBox}>
            <Image
              alt=""
              src={toAtlasImageView2URL(value, {
                mode: 2,
                width: 120,
              })}
              preview={false}
              placeholder={<CardLoading />}
            />
          </div>
          <TrashCanBold
            className={styles.rightIcon}
            onClick={() => {
              onChange?.("");
            }}
          />
        </div>
      ) : (
        <FilePicker
          accept={[".png", ".jpg", ".jpeg"]}
          onChange={handleFileChange}
        >
          {({ openPicker }) => {
            return (
              <Spin spinning={uploadLoading}>
                <div
                  className={styles.imageBox}
                  onClick={async () => {

                    if (!userStore.isLogin) {
                      await wheeLoginPopup({});
                    }

                    if (!userStore.isLogin) {
                      return;
                    }

                    openPicker();
                  }}
                >
                  <div className={styles.leftBox}>
                    <PlusBold />
                  </div>
                  <div className={styles.centerBox}>{t("Upload")}</div>
                  <ChevronRightBlack className={styles.rightIcon} />
                </div>
              </Spin>
            );
          }}
        </FilePicker>
      )}
    </>
  );
});

export default observer(ImageSection);
