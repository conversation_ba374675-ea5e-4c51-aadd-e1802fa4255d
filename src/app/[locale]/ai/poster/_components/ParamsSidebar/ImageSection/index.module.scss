@import "@/styles/variable.scss";

.image {
  :global {
    .image-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0;

      &-switch.ant-switch {
        &.ant-switch-checked,
        &.ant-switch-checked:hover {
          background: $system-content-brand-primary;
        }
      }
    }

    .image-box {

    }
  }
}

.posterUpload {
  width: 100%;
  height: 66px;
  border-radius: 8px;
}

.imageBox {
  box-sizing: content-box;
  height: 40px;
  border-radius: var(--radius-12, 12px);
  border: 1px solid $system-stroke-input;
  background: #fff;
  margin-top: 12px;
  display: flex;
  align-items: center;
  padding: 4px 8px 4px 4px;
  user-select: none;
  cursor: pointer;
  position: relative;

  .leftBox {
    width: 40px;
    height: 40px;
    background: #f6f7fa;
    border-radius: var(--radius-8, 8px);
    border: 1px solid $system-stroke-input;
    background: #F6F7FA;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;

    :global {
      .ant-image {
        width: 100%;
        height: 100%;
        &-img {
          width: 100%;
          height: 100%;
          border-radius: var(--radius-8, 8px);
          object-fit: cover;
        }
      }
    }

    svg {
      width: 16px;
      height: 16px;
      color: #A3AEBF;
      cursor: pointer;
    }
  }

  .centerBox {
    width: 180px;
    color: $system-content-secondary;
    margin-left: 8px;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    text-align: left;
  }

  .rightIcon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;

    svg {
      width: 12px;
      height: 12px;
    }
  }
}

.posterSliderInput {
  margin-top: 12px;

  :global(.ant-input-number-affix-wrapper) {
    width: 58px !important;
  }
}