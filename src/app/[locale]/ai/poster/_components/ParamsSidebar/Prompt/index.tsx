import { useCallback, useMemo, useRef } from "react";
import styles from "./index.module.scss";
import classNames from "classnames";
import { ConfigProvider } from "antd";
import { TrashCanBold } from "@meitu/candy-icons";
import TextArea from "antd/es/input/TextArea";
import { TextAreaRef } from "antd/lib/input/TextArea";
import runes from "runes2";
import _ from "lodash";
import toast from "@/components/Toast";
import { useI18n } from "@/locales/client";

export type TemplateName = "改对象" | "改风格" | "改背景" | "改文字";

type PromptProps = {
  value?: string;
  onChange?: (value: string) => void;
  onSelectTemplate?: (template: TemplateName) => void;
  userChangePrompt?: React.MutableRefObject<boolean>;
  max?: number;
};

// 进行匹配时 开始符号和结束符号一致
const sameMatches = new Set(['"']);

const matches = new Map<string, string>([
  ["【", "】"],
  ...Array.from(sameMatches).map((ch) => [ch, ch] as [string, string]),
]);

const starts = new Set(matches.keys());
const ends = new Set(matches.values());

function getCounter() {
  const sameMatchesCount = new Map<string, number>(
    Array.from(sameMatches).map((ch) => [ch, 0])
  );

  const incSameMatchesCount = (ch: string) => {
    if (sameMatches.has(ch)) {
      sameMatchesCount.set(ch, sameMatchesCount.get(ch)! + 1);
    }
  };

  const isStart = (ch: string) => {
    return (
      starts.has(ch) && (!sameMatches.has(ch) || sameMatchesCount.get(ch)! % 2)
    );
  };

  const isEnd = (ch: string) => {
    return (
      ends.has(ch) && (!sameMatches.has(ch) || !(sameMatchesCount.get(ch)! % 2))
    );
  };

  return {
    incSameMatchesCount,
    isStart,
    isEnd,
  };
}

function Prompt({
  value,
  onChange,
  onSelectTemplate,
  userChangePrompt,
  max = 800,
}: PromptProps) {
  const t = useI18n();

  const el = useRef<TextAreaRef>(null);

  const templates = useMemo(() => {
    return [
      {
        displayKey: "改对象",
        templateValue:
          "将【原对象】更改为【新对象】，保持【需要保留的内容】不变",
      },
      {
        displayKey: "改风格",
        templateValue: "转换为【特定风格】，同时保持【构图/文案/其他】不变",
      },
      {
        displayKey: "改背景",
        templateValue:
          "将背景更改为【新背景内容】，保持主体在完全相同的位置和姿势",
      },
      {
        displayKey: "改文字",
        templateValue: '将"原始文本"替换为"新文本"，保持相同的字体样式',
      },
    ] as Array<{ displayKey: TemplateName; templateValue: string }>;
  }, []);

  const markUserChangePrompt = useCallback(() => {
    if (userChangePrompt) {
      userChangePrompt.current = true;
    }
  }, [userChangePrompt]);

  const errorMessage = useCallback(
    _.debounce(
      () => {
        toast.error(`提示词最大限制${max}字～请修改！`);
      },
      1000,
      {
        leading: true,
        trailing: false,
      }
    ),
    [max]
  );

  const handleChangeWithMaxLimit = (nextValue: string) => {
    if (runes(nextValue).length > max) {
      return errorMessage();
    }

    markUserChangePrompt();
    onChange?.(nextValue);
  };

  const renderTemplatesBtns = () => {
    return templates.map((item) => {
      const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation();
        e.preventDefault();
        onSelectTemplate?.(item.displayKey);

        // 追加当前点击的模版内容
        const attach = () =>
          handleChangeWithMaxLimit(
            (value ?? "") + (value?.length ? "，" : "") + item.templateValue
          );
        // 将当前提示词替换为点击的模版内容
        const replace = () => handleChangeWithMaxLimit(item.templateValue);

        // 如果用户没有手动修改过提示词，执行替换
        if (userChangePrompt && !userChangePrompt.current) {
          replace();
        } else {
          // 否则追加提示词
          attach();
        }
        markUserChangePrompt();

        // 需要等渲染完成后才能拿到正确的scrollHeight
        setTimeout(() => {
          const dom = el.current?.resizableTextArea?.textArea;
          if (dom) {
            dom.scrollTo({ top: dom.scrollHeight, behavior: "smooth" });
          }
        });
      };

      return (
        <button
          key={item.displayKey}
          onClick={handleClick}
          className="prompt-templates-item"
        >
          {item.displayKey}
        </button>
      );
    });
  };

  const handleSelect = () => {
    const dom = el.current?.resizableTextArea?.textArea;

    const select = () => {
      if (!dom || !value || dom.selectionStart !== dom.selectionEnd) {
        return;
      }

      const focus = dom.selectionEnd;
      const { incSameMatchesCount, isStart, isEnd } = getCounter();

      const startStack = [] as Array<{ start: string; index: number }>;
      for (let i = 0; i < value.length; i++) {
        const ch = value[i];

        incSameMatchesCount(ch);

        if (isStart(ch)) {
          startStack.push({ start: ch, index: i });
        }

        if (isEnd(ch)) {
          const item = startStack.pop();
          if (!item || ch !== matches.get(item.start)) {
            continue;
          }

          const startIndex = item.index;
          const end = i;

          if (focus > startIndex && focus <= end) {
            dom.setSelectionRange(startIndex + 1, end);
            return;
          }
        }
      }
    };

    // 同步执行时 无法拿到光标的位置
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        resolve();
        select();
      });
    });
  };

  const handleClear = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onChange?.("");
  };

  return (
    <ConfigProvider>
      <section
        className={classNames(styles.prompt, "sidebar-section")}
        id="poster-prompt"
      >
        <div className="sidebar-section-title">
          <strong>提示词</strong>
        </div>
        <label className="prompt-label">
          <TextArea
            className="prompt-input"
            ref={el}
            value={value}
            onClick={handleSelect}
            onChange={(e) => {
              onChange?.(e.target.value);
              markUserChangePrompt();
            }}
            placeholder="请输入你的创意，在“”中输入文案效果更佳"
            count={{
              max,
              strategy: (txt: string) => runes(txt).length,
              exceedFormatter: (txt: string, { max }: { max: number }) => {
                errorMessage();
                return runes(txt).slice(0, max).join("");
              },
            }}
          />

          <ul className="prompt-templates">{renderTemplatesBtns()}</ul>
          <button className="prompt-clear" onClick={handleClear}>
            <TrashCanBold />
          </button>
        </label>
      </section>
    </ConfigProvider>
  );
}

export default Prompt;
