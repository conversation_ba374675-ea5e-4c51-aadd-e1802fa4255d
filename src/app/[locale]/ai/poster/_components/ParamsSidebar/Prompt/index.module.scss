@import "@/styles/variable.scss";


$footer-height: 32px;
.prompt {
  color: black;
  :global {
    .prompt-label {
      position: relative;
      display: block;
      box-sizing: border-box;
      width: 100%;
      height: 160px;
      padding: 8px 8px 40px;
      border-radius: 12px;
      border: 1px solid $system-stroke-input;
      overflow: hidden;

      .prompt-input {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        resize: none;
        background: $background-input;
        word-break: break-all;
        white-space: wrap;
        border: none;
        box-shadow: none;
        padding: 0;
        caret-color: $system-content-brand-primary;
  
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::placeholder {
          color: #D0D2D6;
        }
  
        &::-webkit-scrollbar {
          display: none;
        }
      }

      .prompt-templates {
        display: flex;
        gap: 4px;
        position: absolute;
        left: 8px;
        bottom: 8px;
        &-item {
          padding: 4px 8px;
          border-radius: 6px;
          border: 1px solid $system-stroke-input;
          background: #fff;
          font-size: 12px;
        }
      }

      .prompt-clear {
        position: absolute;
        right: 8px;
        bottom: 13px;
        svg {
          width: 12px;
          height: 12px;
          color: $system-content-thirdary;
        }
      }
    }
  }
}