import { Form } from "antd";
import { createContext, useContext, useMemo, useRef } from "react";
import { useBaseModelList } from "../useBaseModelList";
import { HardCodeModelFlag } from "../hard-code-model-flag";
import { getRatioSize } from "../getRatioSize";
import { FormInstance } from "antd/lib";
import { EditorConfigBaseModelListResponse } from "@/api/types/editorConfig";
import { SelectionStore } from "../../../_store/selection";
import { ElementName, FabricObject, getElementOptions, ImageElementParams, ImageOptions } from "@meitu/whee-infinite-canvas";
import toast from "@/components/Toast";
import { RenderStore } from "../../../_store/render";
import { observer } from "mobx-react-lite";

type OpenImageEditSwitchOptions = {
  /**
   * 同时设置图生图原图，如果同时打开了autoUploadSelection，则autouploadSelection失效
   */
  shape?: FabricObject;
  /**
   * 自动将当前选中的图片作为原图
   */
  autoUploadSelection?: boolean;
  /**
   * 打开后切换到哪个生图模型
   */
  flag?: HardCodeModelFlag.CommonEdit | HardCodeModelFlag.ProfessionalEdit;
}

type ParamsSidebarContextType = {
  
  register: {
    form: (form: FormInstance) => void;
  },

  context: {
    changeBaseModelId: (id: number, imageEdit: boolean) => void;
    openImageEditSwitch: (options: OpenImageEditSwitchOptions) => void;
    closeImageEditSwitch: () => void;
    baseModelList: EditorConfigBaseModelListResponse[];
    baseModelLoading: boolean;
    validateImageEdit: (shape: FabricObject) => boolean;
  }
}

const noop = () => {};

const ParamsSidebarContext = createContext<ParamsSidebarContextType>({
  register: {
    form: noop,
  },
  context: {
    changeBaseModelId: noop,
    openImageEditSwitch: noop,
    closeImageEditSwitch: noop,
    baseModelList: [],
    baseModelLoading: false,
    validateImageEdit: () => false,
  },
});


type ParamsSidebarContextProviderProps = React.PropsWithChildren<{
  selectionStore: SelectionStore;
  renderStore: RenderStore;
}>;
function ParamsSidebarContextProvider({ children, selectionStore, renderStore }: ParamsSidebarContextProviderProps) {

  const formRef = useRef<FormInstance | null>(null);
  const { list: baseModelList, isLoading: baseModelLoading } = useBaseModelList();
  const singleImage = selectionStore.singleImage;

  const registerForm = (form: FormInstance) => {
    formRef.current = form;
  };

  // 切换模型时，更改画面比例选择
  const changeBaseModelId = (id: number, imageEdit: boolean) => {
    const form = formRef.current;
    if (!form) {
      return;
    }

    const nextBaseModel = baseModelList.find((item) => item.id === id);
    if (!nextBaseModel) {
      return;
    }

    form.setFieldValue("baseModelId", id);

    const currentRatio = form.getFieldValue("ratio");
    const nextRatios = getRatioSize(nextBaseModel, imageEdit);

    if (nextRatios.find((r) => r.ratio === currentRatio)) {
      return;
    }

    form.setFieldsValue({
      ratio: nextRatios[0].ratio,
    });
  };


  const validateImageEdit = (shape: FabricObject) => {
    const render = renderStore.render;
    if (!render) {
      return false;
    }

    const options = getElementOptions.call(render, shape) as ImageOptions;
    if (options._name_ !== ElementName.IMAGE) {
      return false;
    }

    const ratio = options.imageWidth / options.imageHeight;
    if (ratio > 3 || ratio < 1 / 3) {
      toast.error('宽高比例超过3 请重新上传～');
      return false;
    }

    return true;
  }

  /**
   * 打开图生图开关
   * 1. 通过点击图生图开关打开按钮时，切换为【通用编辑模型】
   * 2. 通过选中图片打开图生图时，切换为【通用编辑模型】
   * 3. 通过提示词模版打开图生图时
   *     3.1. 【改对象】、【改风格】、【改背景】切换为【专业编辑模型】
   *     3.2. 【改文字】切换为【通用编辑模型】
   */
  const openImageEditSwitch = ({
    shape,
    flag = HardCodeModelFlag.CommonEdit,
    autoUploadSelection = true,
  }: OpenImageEditSwitchOptions) => {

    const form = formRef.current;
    if (!form) {
      return;
    }

    const render = renderStore.render;
    const getImageSrc = () => {
      
      if (!render) {
        return '';
      }

      let initShape = shape;
      if (autoUploadSelection && !shape) {
        initShape = singleImage?.image;
      }

      if (!initShape) {
        return '';
      }

      const options = getElementOptions.call(render, initShape) as ImageOptions;
      if (options._name_ !== ElementName.IMAGE) {
        return '';
      }

      if (!validateImageEdit(initShape)) {
        throw new Error();
      }

      return options.src;
    }

    try {
      const src =  getImageSrc();
      form.setFieldsValue({
        imageChecked: true,
        image: src,
      });

      const model = baseModelList.find((item) => item.modelFlag === flag);
      if (flag && model) {
        changeBaseModelId(model.id, true);
      }
    } catch (e) {
      
    }
  };

  /**
   * 关闭图生图开关
   * 如果当前选中的是【通用编辑模型】，则切换为【通用生图模型】
   */
  const closeImageEditSwitch = () => {

    const form = formRef.current;
    if (!form) {
      return;
    }

    form.setFieldsValue({
      imageChecked: false,
    });

    const commonEditModel = baseModelList.find(
      (item) => item.modelFlag === HardCodeModelFlag.CommonEdit
    );
    const commonGenerateModel = baseModelList.find(
      (item) => item.modelFlag === HardCodeModelFlag.CommonGenerate
    );
    if (
      commonEditModel &&
      commonGenerateModel &&
      commonEditModel.id === form.getFieldValue("baseModelId")
    ) {
      changeBaseModelId(commonGenerateModel.id, false);
    }
  };

  const value = useMemo(() => ({
    register: {
      form: registerForm,
    },
    context: {
      baseModelList,
      baseModelLoading,
      changeBaseModelId,
      openImageEditSwitch,
      closeImageEditSwitch,
      validateImageEdit,
    }
  }), [baseModelList, baseModelLoading])


  return (
    <ParamsSidebarContext.Provider
      value={value}
    >
      {children}
    </ParamsSidebarContext.Provider>
  );
}

export default observer(ParamsSidebarContextProvider);

export function useParamsSidebarContext() {
  const context = useContext(ParamsSidebarContext);
  if (!context) {
    throw new Error("useParamsSidebarContext must be used within a ParamsSidebarProvider");
  }
  return context.context;
}

export function useParamsSidebarContextRegister() {
  const context = useContext(ParamsSidebarContext);
  if (!context) {
    throw new Error("useParamsSidebarContextValue must be used within a ParamsSidebarProvider");
  }
  return context.register;
}