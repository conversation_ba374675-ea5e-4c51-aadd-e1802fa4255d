import { observer } from "mobx-react-lite";
import styles from './index.module.scss';
import classNames from "classnames";
import { CheckBlack, ChevronRightBlack, InfoCircle } from "@meitu/candy-icons";
import { Image, Popover, Spin, Typography } from "antd";
import { EditorConfigBaseModelListResponse } from "@/api/types/editorConfig";
import { useMemo, useState } from "react";

type BaseModelProps = {
  /**
   * 正在加载模型列表
   */
  loading?: boolean;

  /**
   * 待选择的列表
   */
  list?: EditorConfigBaseModelListResponse[];
  /**
   * 不可选择的雷彪
   */
  disabledIdList?: number[];

  /**
   * 当前被选中的model id
   */
  value?: number;

  onChange?: (id: number) => void;
}


function BaseModel({
  loading = true,
  list = [],
  disabledIdList = [],
  value,
  onChange,
}: BaseModelProps) {

  const activeModel = useMemo(() => {
    return list.find((item) => item.id === value);
  }, [list, value]);

  const [popoverVisible, setPopoverVisible] = useState(false);
  const disabledIds = useMemo(() => {
    return new Set(disabledIdList);
  }, [disabledIdList]);

  const renderPopover = () => {
    return (
      <div className="model-popover">
        <div className="model-popover-header">
          <span className="model-popover-header-title">选择模型</span>
          <span className="model-popover-header-subtitle">通用编辑模型</span>
        </div>

        <ul className="model-popover-list">
          {
            list.map((model) => {
              const isSelected = value === model.id;

              const disabled = disabledIds.has(model.id);

              const handleClick = () => {
                if (disabled) {
                  return;
                }

                onChange?.(model.id);
                setPopoverVisible(false);
              }

              return (
                <li
                  key={model.id}
                  className={classNames("model-item", isSelected && "selected", disabled && "disabled")}
                  onClick={handleClick}
                >
                  <Image rootClassName="model-item-img" src={model.images?.[0]} preview={false} />
                  <div className="model-item-desc">
                    <Typography.Text
                      className="model-item-desc-title"
                      ellipsis={{
                        tooltip: true,
                      }}
                    >
                      {model.name}
                    </Typography.Text>
                    <Typography.Text
                      className="model-item-desc-subtitle"
                      ellipsis={{
                        tooltip: true,
                      }}
                    >
                      {model.desc}
                    </Typography.Text>
                  </div>

                  { isSelected && <CheckBlack className="model-item-selected-icon"/> }
                </li>
              );
            })
          }
        </ul>
      </div>
    );
  }

  const renderLoading = () => {
    return (
      <Spin wrapperClassName="model-main-loading" spinning>
        <div className="model-main-card">
        </div>
      </Spin>
    )
  }

  const renderModelCard = () => {
    return (
      <Popover
        open={popoverVisible}
        onOpenChange={open => setPopoverVisible(open)}
        trigger="click"
        title={renderPopover()}
        placement="rightTop"
        align={{
          offset: [16, -44],
        }}
        arrow={false}
        classNames={{ root: styles.popover }}
      >
        <div className="model-main-card">
          <Image rootClassName="model-main-card-img" preview={false} src={activeModel?.images?.[0]}/>
          <div className="model-main-card-desc">
            <Typography.Text
              className="model-main-card-desc-title"
              ellipsis={{
                tooltip: true,
              }}
            >
              {activeModel?.name}
            </Typography.Text>
            <Typography.Text  
              className="model-main-card-desc-subtitle"
              ellipsis={{
                tooltip: true,
              }}
            >
              {activeModel?.desc}
            </Typography.Text>
          </div>
          <ChevronRightBlack className="model-main-card-icon"/>
        </div>
      </Popover>
    );
  }

  return (
    <section className={classNames(styles.model, "sidebar-section")}>
      <div className="model-title sidebar-section-title">
        <div className="model-title-content">
          <strong>模型</strong>
        </div>
        {
          activeModel?.tips && (
            <div className="model-title-tips">
              <InfoCircle className="model-title-tips-icon"/>
              <span className="model-title-tips-text">{activeModel.tips}</span>
            </div>
          )
        }
      </div>

      <div className="model-main">
        { loading ? renderLoading() : renderModelCard() }
      </div>
    </section>
  )
}

export default observer(BaseModel);