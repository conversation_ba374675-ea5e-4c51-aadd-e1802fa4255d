@import "@/styles/variable.scss";

.model {
  :global {
    .model-title {
      display: flex;
      justify-content: space-between;

      .model-title-tips {
        font-size: 12px;
        color: $system-content-thirdary;
        display: flex;
        align-items: center;
        .model-title-tips-icon {
          margin-right: 2px;
          svg {
            width: 14px;
            height: 14px;
          }
        }
      }
    }

    .model-main {

      &-loading {
        .ant-spin-container.ant-spin-blur{
          &::after {
            background: transparent;
          }
        }
      }

      &-card {
        display: flex;
        align-items: center;
        box-sizing: content-box;
        width: 276px;
        height: 40px;
        padding: 6px;
        border: 1px solid $system-stroke-input;
        border-radius: 12px;
        cursor: pointer;
        user-select: none;
        &-img {
          flex: 0 0 auto;
          width: 40px;
          height: 40px;
          border-radius: 8px;
          margin-right: 8px;
          border-radius: 8px;
          overflow: hidden;

          .ant-image-img {
            width: 100%;
            height: 100%;
            object-fit: cover;

          }
        }

        &-desc {
          flex: 1 1 0;
          min-width: 0;

          display: flex;
          flex-direction: column;
          
          &-title {
            color: $system-content-secondary;
            font-size: 14px;
          }

          &-subtitle {
            color: $system-content-thirdary;
            font-size: 12px;
          }
        }

        &-icon {
          flex: 0 0 auto;
          margin-left: 8px;
          svg {
            width: 12px;
            height: 12px;
            color: $system-content-secondary;
          }
        }
      }
    }
  }
}

.popover {
  :global {
    .ant-popover-content {
      .ant-popover-inner {
        background: $background-system-frame-float-panel;
        border-radius: 12px;
        .ant-popover-title {
          margin: 0;

          .model-popover-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            &-title {
              color: $system-content-secondary;
              font-size: 14px;
            }
            &-subtitle {
              color: $system-content-thirdary;
              font-size: 12px;
              margin-left: 8px;
            }
          }

          .model-popover-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            .model-item {
              display: flex;
              box-sizing: content-box;
              width: 242px;
              height: 40px;
              padding: 6px 8px 6px 6px;
              border-radius: 12px;
              border: 1px solid $system-stroke-input;
              user-select: none;
              cursor: pointer;

              &.selected {
                border-color: $system-content-brand-primary;
              }

              &-img {
                flex: 0 0 auto;
                width: 40px;
                height: 40px;
                border-radius: 6px;
                overflow: hidden;
                .ant-image-img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }

              &-desc {
                flex: 1 1 auto;
                margin: 0 8px;
                min-width: 0;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                &-title {
                  color: $system-content-secondary;
                  font-size: 14px;
                }

                &-subtitle {
                  color: $system-content-thirdary;
                  font-size: 12px;
                }
              }

              &-selected-icon {
                flex: 0 0 auto;
                margin-right: 16px;
                svg {
                  width: 16px;
                  height: 16px;
                  color: $system-content-brand-primary;
                }
              }

              &.disabled {
                cursor: not-allowed;
                opacity: 0.5;
              }
            }
          }
        }
      }
    }
  }
}