import { fetchPosterModel } from "@/api/aiPoster/editor";
import { useMemo, useState } from "react";
import useSWR from "swr";

export function useBaseModelList() {

  const { data, error, isLoading } = useSWR('/editor/ai_poster_model.json', () => fetchPosterModel());

  const list = useMemo(() => {
    return data?.baseModel.flatMap(category => category.list) || [];
  }, [data])

  return {
    list,
    error,
    isLoading,
  };
}