"use client";
import { Tabs } from "antd";
import InfiniteScroll from "react-infinite-scroll-component";
import { observer } from "mobx-react";
import { useRootStore } from "@/app/[locale]/ai/poster/_store";
import { startTransition, useMemo, useState } from "react";
import { TemplateItem } from "./TemplateItem";
import { TabsProps } from "antd/lib";
import classNames from "classnames";
import { useSaveCanvas } from "@/app/[locale]/ai/poster/_hooks/useSaveProjectHistory";
import { useI18n } from "@/locales/client";
import { toJS } from "mobx";
import _ from "lodash";
import { Loading } from "@/components";
import styles from "./index.module.scss";
import { trackEvent } from "@/services/tracer";
import { TemplateExposureLocation } from "@/types/tracking";
import { useStore } from "@/contexts/StoreContext";
import { useInsertTemplatePreview } from "../../../_hooks/useInsertTemplatePreview";
import { useParamsSidebarContext } from "../../ParamsSidebar/context";
import { FabricObject } from '@meitu/whee-infinite-canvas';
import { useImg2ImgFlag } from "../../ParamsSidebar/context/img2img-mark";

type TemplateProps = {
  disabled?: boolean;
};

function Template({ disabled }: TemplateProps) {
  const rootStore = useRootStore();
  const { userStore } = useStore();
  const { configStore, renderStore, editorStatusStore, projectsStore } = rootStore;
  const posterConfigStore = configStore.posterConfigStore;
  const templateList = configStore.templateList;
  const activeTab = configStore.activeCategoryId;
  const { submitSaveElements } = useSaveCanvas(rootStore);
  const t = useI18n();
  const [loadingTemplates, setLoadingTemplates] = useState<
    Map<number, boolean>
  >(new Map());

  const insertTemplatePreview = useInsertTemplatePreview(rootStore);
  const { openImageEditSwitch } = useParamsSidebarContext();
  const { enableImg2Img } = useImg2ImgFlag({ renderStore, projectsStore });

  const render = renderStore.render;
  const historyPlugins = renderStore.historyPlugins;
  const items = useMemo(() => {
    return templateList.map((category) => {
      // const isLoading = posterConfigStore.isLoading(category.categoryId);
      const isLastPage = posterConfigStore.isLastPage(category.categoryId);
      const nextPage = () =>
        posterConfigStore.fetchConfig({
          mode: "append",
          categoryId: category.categoryId,
          count: 20,
        });

      return {
        // Tabs组件的key要求string类型
        key: category.categoryId + "",
        label: category.categoryName,
        children: (
          <div
            id={`_scroll-container-template--${category.categoryId}`}
            className="template-list-container"
            ref={(el) => {
              if (category.categoryId === activeTab) {
                editorStatusStore.templateScrollContainer = el;
              }

              return () => {
                editorStatusStore.templateScrollContainer = null;
              };
            }}
          >
            <InfiniteScroll
              style={{ overflow: "hidden" }}
              next={nextPage}
              hasMore={!isLastPage}
              loader={<Loading />}
              dataLength={category.list.length}
              scrollableTarget={`_scroll-container-template--${category.categoryId}`}
              endMessage={
                <div className="template-list-tips">
                  {t("You've reached the end!")}
                </div>
              }
            >
              <div className="template-list-content">
                {_.chunk(toJS(category.list), 2).map((templateRow, i) => {
                  return (
                    <div key={i} className="template-row">
                      {templateRow.map((template) => {
                        const handleClick = async () => {
                          if (!render || loadingTemplates.get(template.id)) {
                            return;
                          }
                          trackEvent("template_feed_click", {
                            template_category_id: category.categoryId,
                            template_id: template.id,
                            location: TemplateExposureLocation.Editor,
                          });
                          setLoadingTemplates((prev) =>
                            new Map(prev).set(template.id, true)
                          );

                          // 插入模板预览图
                          insertTemplatePreview(template)
                            .then((image?: FabricObject ) => {
                              if (!image) {
                                return;
                              }

                              openImageEditSwitch({ shape: image });
                              return enableImg2Img(image);
                            })
                            .finally(() => {
                              setLoadingTemplates((prev) =>
                                new Map(prev).set(template.id, false)
                              );
                            });
                        };
                        return (
                          <TemplateItem
                            key={template.id}
                            item={template}
                            onClick={handleClick}
                            disabled={disabled}
                            loading={loadingTemplates.get(template.id) === true}
                          />
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            </InfiniteScroll>
          </div>
        ),
      };
    });
  }, [
    templateList,
    loadingTemplates,
    render,
    posterConfigStore.paginationStatusBook,
  ]);

  const renderTabBar: TabsProps["renderTabBar"] = (props) => (
    <div
      className="template-category-list"
      ref={(el: HTMLDivElement) => {
        const handleTabContainerWheel = (event: WheelEvent) => {
          /**
           * 直接通过props中的onWheel绑定时 无法调用preventDefault
           */
          event.preventDefault();
          const { deltaX, deltaY } = event;
          const tan = deltaY / deltaX;
          // 纵向滚动
          if (Math.abs(tan) > 1) {
            el?.scroll({
              left: el.scrollLeft + deltaY,
              behavior: "instant",
            });
          } else {
            el?.scroll({
              left: el.scrollLeft + deltaX,
              behavior: "instant",
            });
          }
        };

        el?.addEventListener("wheel", handleTabContainerWheel);

        return () => {
          el?.removeEventListener("wheel", handleTabContainerWheel);
        };
      }}
    >
      {templateList?.map((item) => {
        return (
          <button
            key={String(item.categoryId)}
            disabled={disabled}
            className={classNames(
              "template-category-tab",
              item.categoryId === activeTab && "active"
            )}
            onClick={() => {
              startTransition(() => {
                configStore.setActiveCategoryId(item.categoryId);
              });
            }}
            ref={(el) => {
              if (el) {
                editorStatusStore.templateTabs.set(item.categoryId, el);
              }

              return () => {
                editorStatusStore.templateTabs.delete(item.categoryId);
              };
            }}
          >
            {item.categoryName}
          </button>
        );
      })}
    </div>
  );

  return (
    <div className={styles.template}>
      <Tabs
        items={items}
        activeKey={String(activeTab)}
        renderTabBar={renderTabBar}
      />
    </div>
  );
}

export default observer(Template);
