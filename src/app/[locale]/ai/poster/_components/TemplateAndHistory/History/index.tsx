import { useRootStore } from "@/app/[locale]/ai/poster/_store";
import { observer } from "mobx-react";
import React, { Fragment, useState } from "react";
import { CheckBlack, TrashCan } from "@meitu/candy-icons";
import { useMount } from "react-use";
import InfiniteScroll from "react-infinite-scroll-component";
// import { Loading } from '@/components';
import { Image } from "antd";
import { toAtlasImageView2URL } from "@meitu/util";
import classNames from "classnames";
import { AIPoster, AlphaStatus } from "@/api/types/aiPoster/task";
import RemoveModal, { RemoveItem } from "./RemoveModal";
import { getImageEditStatus } from "@/app/[locale]/ai/poster/_utils/imageEdit";
import { createImage } from "@meitu/whee-infinite-canvas";
// import { useApp } from '@/App';
import { useSaveCanvas } from "@/app/[locale]/ai/poster/_hooks/useSaveProjectHistory";
import styles from "./index.module.scss";
import { Loading } from "@/components";
import { useI18n } from "@/locales/client";
import Lottie from "lottie-react";
import loadingAnimation from "@/assets/lottie/poster/imageLoading/data.json";
import { NoHistory } from "@/assets/icons";

const equalsBatchRemoveSelectedItem = (e1: RemoveItem, e2: RemoveItem) => {
  return e1.image?.urlShort === e2.image?.urlShort && e1.task.id === e2.task.id;
};

type BatchRemove = {
  /**
   * 是否开启批量删除
   */
  enabled: boolean;
  /**
   * 批量删除选中的图片
   */
  selected: Array<RemoveItem>;
};

function History() {
  const { generateRecordStore, renderStore } = useRootStore();

  const t = useI18n();
  // const { openRefreshModal } = useApp();
  const rootStore = useRootStore();
  const { submitSaveElements } = useSaveCanvas(rootStore);
  const [loadingImages, setLoadingImages] = useState<Map<string, boolean>>(
    new Map()
  );

  // 批量删除模式
  const [batchRemove, setBatchRemove] = useState<BatchRemove>({
    enabled: false,
    selected: [],
  });
  const openBatchRemoveMode = () => {
    if (batchRemove.enabled) {
      return;
    }

    setBatchRemove({
      enabled: true,
      selected: [],
    });
  };

  const closeBatchRemoveMode = () => {
    if (!batchRemove.enabled) {
      return;
    }

    setBatchRemove({
      enabled: false,
      selected: [],
    });
  };

  // 删除弹窗
  const [removeModalProps, setRemoveModalProps] = useState({
    open: false,
    removeItems: [] as Array<RemoveItem>,
  });

  useMount(() => {
    generateRecordStore.fetchHistory({ enterStatus: "loading" });
  });

  const tasksList = generateRecordStore.list;
  const fetchNext = () => {
    // generateRecordStore.fetchHistory({ enterStatus: 'updating' });
  };

  const renderRemoveController = () => {
    const removeMode = batchRemove.enabled;

    if (!removeMode) {
      return (
        <div className="remove-mode-btn" onClick={openBatchRemoveMode}>
          <span className="remove-mode-btn-label">
            {t("generate-records.batch-deletion")}
          </span>
          <TrashCan className="remove-mode-btn-icon" />
        </div>
      );
    }

    const handleBatchRemove = () => {
      setRemoveModalProps({
        open: true,
        removeItems: batchRemove.selected.slice(0),
      });
    };

    const selectedCounts = batchRemove.selected.length;
    return (
      <div className="remove-controller">
        <div className="remove-controller-selected">
          {t("Selected")}（{selectedCounts}）
        </div>
        <div className="remove-controller-btns">
          {selectedCounts > 0 && (
            <div
              className="remove-controller-btns-item ok"
              onClick={handleBatchRemove}
            >
              {t("Confirm")}
            </div>
          )}
          <div
            className="remove-controller-btns-item cancel"
            onClick={closeBatchRemoveMode}
          >
            {t("Cancel")}
          </div>
        </div>
      </div>
    );
  };

  const renderList = () => {
    const batchMode = batchRemove.enabled;

    return (
      <InfiniteScroll
        scrollableTarget="poster-records-scroll-container"
        className="infinite-scroll"
        dataLength={tasksList.length}
        next={fetchNext}
        hasMore={!tasksList.length}
        loader={<Loading />}
        endMessage={false}
      >
        <div className="infinite-scroll-content">
          {tasksList.map((task) => {
            // 有透明通道的参数的不展示，替换完之后再展示
            const results = task.resultImages.filter(
              (item) => item.alphaStatus !== AlphaStatus.HasAlpha
            );

            return results.map((r, index) => {
              const key =
                `t${task.id}` + (r.urlShort ? `u${r.urlShort}` : `i${index}`);

              if (task.loadingStatus === AIPoster.LoadingStatus.Loading) {
                return (
                  <div key={key} className="record-item loading">
                    <Loading />
                  </div>
                );
              }

              const item: RemoveItem = { task, image: r };

              // 这张图片在批量删除中被选中
              const hasBatchSelected =
                batchRemove.enabled &&
                !!batchRemove.selected.find((selected) =>
                  equalsBatchRemoveSelectedItem(item, selected)
                );

              const handleBatchSelect = () => {
                // 如果没开启批量删除 不处理
                if (!batchRemove.enabled) {
                  return;
                }

                const batchSelected = batchRemove.selected;
                let nextBatchSelected = batchSelected;
                // 如果已经被选中 再次选择户已取消选中
                if (hasBatchSelected) {
                  nextBatchSelected = batchSelected.filter(
                    (s) => !equalsBatchRemoveSelectedItem(item, s)
                  );
                } else {
                  nextBatchSelected = batchSelected.concat(item);
                }

                setBatchRemove({ enabled: true, selected: nextBatchSelected });
              };

              const handleRemove = () => {
                setRemoveModalProps({
                  open: true,
                  removeItems: [item],
                });
              };

              if (
                task.loadingStatus !== AIPoster.LoadingStatus.Success ||
                r.imageStatus !== AIPoster.ImageStatus.Success
              ) {
                return null;
                // return (
                //   <RemovableItem
                //     key={key}
                //     className="record-item error"
                //     batchMode={batchMode}
                //     selected={hasBatchSelected}
                //     onSelect={handleBatchSelect}
                //     onRemove={handleRemove}
                //   >
                //     <div className="error-content">
                //       <ExclamationmarkTriangle className="error-content-icon"/>
                //       <div className="error-content-tips">
                //         任务失败
                //         <br/>
                //         请重新提交
                //       </div>
                //     </div>
                //   </RemovableItem>
                // );
              }

              const previewUrl =
                r.urlSign &&
                toAtlasImageView2URL(r.urlSign, {
                  mode: 2,
                  width: 107 * window.devicePixelRatio,
                  height: 105 * window.devicePixelRatio,
                });

              const handleInsertEditor = () => {
                if (!renderStore.render || loadingImages.get(r.urlSign)) {
                  return;
                }

                // 记录插入图片
                setLoadingImages((prev) => new Map(prev).set(r.urlSign, true));

                const render = renderStore.render;
                const historyPlugins = renderStore.historyPlugins;
                createImage("", {
                  src: r.urlSign,
                  _custom_data_history_: {
                    ...getImageEditStatus(task.taskCategory),
                    params: task.params,
                    msgId: task.id,
                    urlShort: r.urlShort,
                  },
                })
                  .catch(() => {
                    // openRefreshModal();
                    return null;
                  })
                  .then((group) => {
                    if (!group) {
                      return;
                    }
                    render.addToViewPortCenterByArrangement(group);
                    render._FC.setActiveObject(group);
                    render.backToOriginPosition({ target: group });
                    render._FC.requestRenderAll();
                    const operation =
                      historyPlugins?.baseAction.getAddOperation({
                        objects: [group],
                      });
                    if (operation) {
                      historyPlugins?.submit(operation);
                    }
                    submitSaveElements([group]);
                  })
                  .finally(() => {
                    // 插入完成后 取消加载状态
                    setLoadingImages((prev) =>
                      new Map(prev).set(r.urlSign, false)
                    );
                  });
              };

              return (
                <RemovableItem
                  key={key}
                  className="record-item success"
                  batchMode={batchMode}
                  selected={hasBatchSelected}
                  onSelect={handleBatchSelect}
                  onRemove={handleRemove}
                  onClick={handleInsertEditor}
                >
                  <Image
                    src={toAtlasImageView2URL(previewUrl, {
                      mode: "0" as any,
                      width: 107 * window.devicePixelRatio,
                      height: 107 * window.devicePixelRatio,
                    })}
                    placeholder={<Loading />}
                    alt=""
                    preview={false}
                    loading="lazy"
                  />

                  {loadingImages.get(r.urlSign) === true && (
                    <div className={"history-item-loading"}>
                      <Lottie
                        className={"loading-box"}
                        animationData={loadingAnimation}
                        autoplay
                        loop
                      />
                    </div>
                  )}
                </RemovableItem>
              );
            });
          })}
        </div>
      </InfiniteScroll>
    );
  };

  //#region 不同状态的渲染函数
  const renderLoading = () => {
    return <div className="empty-container">{/* <Loading /> */}</div>;
  };
  const renderLoaded = () => {
    return (
      <Fragment>
        {renderRemoveController()}
        <div className="record-list" id="poster-records-scroll-container">
          {renderList()}
        </div>
      </Fragment>
    );
  };
  const renderLoadedEmpty = () => {
    return (
      <div className="empty-container">
        <NoHistory />
        <div>{t("No historical records")}</div>
      </div>
    );
  };
  const renderError = () => {
    return (
      <div className="empty-container">{t("No historical records")}</div>
    );
  };
  //#endregion

  return (
    <Fragment>
      <div className={styles.history}>
        {(generateRecordStore.status === "init" ||
          generateRecordStore.status === "loading") &&
          renderLoading()}

        {(generateRecordStore.status === "loaded" ||
          generateRecordStore.status === "updating") &&
          (generateRecordStore.successImagesCount
            ? renderLoaded()
            : renderLoadedEmpty())}

        {generateRecordStore.status === "error" && renderError()}
      </div>

      <RemoveModal
        {...removeModalProps}
        onCancel={() => {
          setRemoveModalProps({ open: false, removeItems: [] });
          closeBatchRemoveMode();
        }}
        onFinish={() => {
          setRemoveModalProps({ open: false, removeItems: [] });
          closeBatchRemoveMode();
        }}
      />
    </Fragment>
  );
}

function SelectedMask() {
  return (
    <div className={styles["selected-mask"]}>
      <div className="selected-mask-corner">
        <CheckBlack className="selected-mask-corner-icon" />
      </div>
    </div>
  );
}

type HoverMaskProps = React.PropsWithChildren<{
  className?: string;
}>;
function HoverMask({ children, className }: HoverMaskProps) {
  return (
    <div className={classNames(styles["hover-mask"], className)}>
      {children}
    </div>
  );
}

type RemovableItemProps = React.PropsWithChildren<{
  className?: string;
  /**
   * 批量删除模式只能选择 不能删除
   */
  batchMode?: boolean;
  /**
   * 批量删除时被选中
   */
  selected?: boolean;
  /**
   * 批量删除时 触发选择
   */
  onSelect?: () => any;
  /**
   * 在非批量删除时 点击触发的事件
   */
  onClick?: () => any;
  /**
   * 单独删除
   */
  onRemove?: () => any;
}>;
function RemovableItem({
  children,
  batchMode,
  className,
  selected,
  onSelect,
  onRemove,
  onClick,
}: RemovableItemProps) {
  return (
    <div
      className={className}
      onClick={() => {
        if (batchMode) {
          onSelect?.();
          return;
        }

        onClick?.();
      }}
    >
      {children}
      {batchMode && selected && <SelectedMask />}
      {!batchMode && (
        <HoverMask>
          <div
            className="remove-btn"
            onClick={(e) => {
              e.stopPropagation();
              e.nativeEvent.stopImmediatePropagation();
              // console.log('on remove');
              onRemove?.();
            }}
          >
            <TrashCan />
          </div>
        </HoverMask>
      )}
    </div>
  );
}

export default observer(History);
