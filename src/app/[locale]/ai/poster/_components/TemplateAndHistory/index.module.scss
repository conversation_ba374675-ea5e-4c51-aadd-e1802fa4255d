@import '@/styles/variable.scss';
@import './variables.scss';
@import '@/app/[locale]/ai/poster/_components/zIndex.scss';

$drawer-width: 254px;
$drawer-background: var(--system-background-secondary, #fff);

.drawer {
  position: absolute;
  z-index: $z-index-template-and-history;
  width: $drawer-width;
  height: 100%;
  bottom: 0;
  right: 0;
  transform: translateX($drawer-width);
  transition: transform 0.3s ease-in-out;
  user-select: none;

  &:global(.open) {
    transform: translate(0);

    :global(.drawer-trigger-icon) {
      transform: rotate(180deg);
    }
  }

  &:global(.disabled) {
    opacity: 0.7;
    cursor: not-allowed;
  }

  :global {
    .drawer-trigger {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translate(-100%, -50%);
      width: 16px;
      height: 72px;
      border-radius: 8px 0 0 8px;
      background: $drawer-background;
      box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
        0px 6px 24px 0px rgba(0, 0, 0, 0.06);
      cursor: pointer;

      .drawer-trigger-icon {
        svg {
          width: 14px;
          height: 14px;
          color: $system-content-thirdary;
        }
      }
    }

    .drawer-content {
      width: $drawer-width;
      height: 100%;
      background: $drawer-background;
      box-shadow: -1px 0 0 0 $system-stroke-input;
      overflow: hidden;
    }
  }
}

.tabs {
  &:global {
    &.ant-tabs {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    &>.ant-tabs-nav {
      padding: 0 16px;
      flex: none !important;

      &::before {
        border-color: $system-stroke-input !important;
      }

      &>.ant-tabs-nav-wrap {
        &>.ant-tabs-nav-list {
          width: 100%;
          &>.ant-tabs-tab {
            width: 100%;
            justify-content: center;
            &>.ant-tabs-tab-btn {
              color: $system-content-thirdary;
            }

            &.ant-tabs-tab-active {
              &>.ant-tabs-tab-btn {
                color: $system-content-secondary;
                font-weight: 600;
                text-shadow: none;
              }
            }

            &.ant-tabs-tab-focus {
              &>.ant-tabs-tab-btn {
                outline: none;
              }
            }
          }

          &>.ant-tabs-ink-bar {
            background-color: $system-content-brand-thirdary;
          }
        }
      }
    }

    &>.ant-tabs-content-holder {
      flex: 1 !important;
      min-height: 0 !important;
      overflow: hidden;

      &>.ant-tabs-content {
        height: 100%;
        overflow: hidden;

        &>.ant-tabs-tabpane {
          height: 100%;
          overflow: hidden;
        }
      }
    }

    &.disabled {
      &>.ant-tabs-nav {
  
        &>.ant-tabs-nav-wrap {
          &>.ant-tabs-nav-list {
            &>.ant-tabs-tab {
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }
}