import { observer } from "mobx-react";
import Drawer from "./Drawer";
import { Tabs } from "antd";
import Template from "./Template";
import History from "./History";
import { useRootStore } from "@/app/[locale]/ai/poster/_store";
import styles from "./index.module.scss";
import { useI18n } from "@/locales/client";
import { RightSiderTabsKey } from "../../_store/editorStatus";
import classNames from "classnames";

type TemplateAndHistoryProps = {
  disabled?: boolean;
};

function TemplateAndHistory({
  disabled: propsDisabled,
}: TemplateAndHistoryProps) {
  const { projectsStore, editorStatusStore } = useRootStore();
  const isOpen = editorStatusStore.isRightSiderOpen;
  const setIsOpen = (open: boolean) =>
    editorStatusStore.setIsRightSiderOpen(open);
  const t = useI18n();

  const disabled = propsDisabled || editorStatusStore.globalDisabled;

  const items = [
    {
      key: RightSiderTabsKey.Template,
      label: t("Templates"),
      children: <Template disabled={disabled} />,
    },
    {
      key: RightSiderTabsKey.History,
      label: t("History"),
      children: <History key={projectsStore.activeProjectId} />,
    },
  ];

  const activeTab = editorStatusStore.rightSiderActiveTab;
  const handleActiveTabChange = (tab: string) => {
    if (disabled) {
      return;
    }
    editorStatusStore.setRightSiderActiveTab(tab as RightSiderTabsKey);
  };

  return !editorStatusStore.isClearScreenUI ? (
    <Drawer open={isOpen} onOpenChange={setIsOpen} disabled={disabled}>
      <Tabs
        id="poster-right-tabs"
        items={items}
        rootClassName={classNames(styles.tabs, { disabled })}
        tabBarGutter={12}
        activeKey={activeTab}
        onChange={handleActiveTabChange}
      />
    </Drawer>
  ) : null;
}

export default observer(TemplateAndHistory);
