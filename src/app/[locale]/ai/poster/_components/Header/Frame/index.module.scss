@import '@/styles/variable.scss';
@import '@/app/[locale]/ai/poster/_components/zIndex.scss';
@import '../header-common.scss';

.frame {
  @include header-button-mixin;
  margin-left: 8px;
}

.sider:global(.ant-drawer) {
  position: absolute;
  z-index: $z-index-params-drawer;
  height: calc(100% - $header-height);
  top: $header-height;
  left: 0;
  outline: none;
  user-select: none;

  :global {
    .ant-drawer-content-wrapper {
      .ant-drawer-content {
        .ant-drawer-body {
          padding: 0;
          scrollbar-width: none;
          -ms-overflow-style: none;
          &::-webkit-scrollbar {
            display: none;
          }
          
          .dimensions-collapse.ant-collapse {
            border-right: 1px solid var(--system-stroke-input-default, #22272E);
            .ant-collapse-item {
              padding: 8px 4px;
              .ant-collapse-header {
                padding: 8px;
                font-size: 14px;
                color: #fff;
                .ant-collapse-expand-icon {
                  padding-right: 8px;
                  .expand-icon {
                    font-size: 12px;
                    &.active {
                      transform: rotate(90deg);
                    }
                  }
                }
              }

              .ant-collapse-content {
                &-box {
                  padding: 0;
                  .dimensions {
                    .dimensions-item {
                      font-size: 14px;
                      padding: 8px 8px 8px 28px;
                      border-radius: 10px;
                      display: flex;
                      justify-content: space-between;
                      position: relative;
                      
                      .name {
                        color: #fff;
                      }
                      
                      .value {
                        color: $system-content-thirdary;
                      }
                      
                      &:hover {
                        background: $system-background-thirdary;
                        &::after{
                          display: block;
                          content:'';
                          position: absolute;
                          top: 0;
                          right: 0;
                          bottom: 0;
                          left:0;
                          border-radius: 10px;
                          border: 1px solid var(--system-stroke-button, #323B48);
                        }
                        .name {
                          font-weight: bolder;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
