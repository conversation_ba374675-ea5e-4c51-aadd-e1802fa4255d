@import '../header-common.scss';

.cursor {
  display: flex;
  align-items: center;
  margin-left: 24px;
  flex: 0 0 auto;
  :global {
    .cursor-button {
      @include header-button-mixin;
    }

    .cursor-change-wrapper{
      margin-left: 4px;
      width: 12px;
      height: 32px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--radius-4, 4px);
      position: relative;
      .cursor-change {
        @include dropdown-trigger-mixin;
      }

      &:hover{
        background: $system-background-seventh;

        &::after{
          display: block;
          content:'';
          position: absolute;
          top:0;
          right: 0;
          bottom: 0;
          left: 0;
          border-radius: var(--radius-4, 4px);
        }
      }


    }
  }
}

.cursor-overlay {
  @include popover-mixin;
}

.cursor-item {
  @include dropdown-item-mixin;
}
