import { createPortal } from "react-dom";
import AreaSelectionPanel from "../../../AreaSelectionPanel";
import { useEffect, useMemo, useRef, useState } from "react";
import { useRootStore } from "@/app/[locale]/ai/poster/_store";
import { SelectionTool } from "../../../AreaSelectionPanel/types";
import {
  ElementName,
  FabricObject,
  getElementOptions,
  Group,
  IImage,
  LassoBrush,
  LoadingType,
  PathBrush,
  RectBrush,
  RenderMaskCursor,
  WarningType,
} from "@meitu/whee-infinite-canvas";
import toast from "@/components/Toast";
import { useI18n } from "@/locales/client";
import {
  PaintBold,
  SelectionBoxBold,
  SelectionLassoBold,
} from "@meitu/candy-icons";
import PaintSize from "../../../AreaSelectionPanel/tool-params/PaintSize";
import styles from "./index.module.scss";
import QuantitySection from "../../../AreaSelectionPanel/params-options/QuantitySection";
import { Form } from "antd";
import { editorLayoutContent } from "@/app/[locale]/ai/poster/_constant/element";
import { textEditBrushOptions } from "./constants";
import { observer } from "mobx-react-lite";
import { toSnakeCase } from "@meitu/util";
import runes from "runes2";
import { createPureUpload } from "@/utils/uploader";
import { HeaderAction } from "@/app/[locale]/ai/poster/_store/headerAction";
import { redrawImage } from "@/utils/redraw";
import { AIPoster, Submit } from "@/api/types/aiPoster/task";
import { submitTask } from "@/api/aiPoster/task";
import { useStore } from "@/contexts/StoreContext";
import { dispatch } from "@/app/[locale]/ai/poster/_utils/TaskDispatcher";
import { uploadErrorhandler } from "@/utils/error-handler/upload";
import { handleSubmitError } from "@/app/[locale]/ai/poster/_utils/submit";

import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import SubmitButton from "../../../AreaSelectionPanel/SubmitButton";
import CancelAreaButton from "../../../AreaSelectionPanel/CancelAreaButton";
import { fetchPriceDesc } from "@/api/meidou";
import { FunctionCode } from "@/api/types/meidou";

function TextEditPanel() {
  const { userStore } = useStore();
  const rootStore = useRootStore();
  const {
    selectionStore,
    renderStore,
    projectsStore,
    headerActionStore,
    editorStatusStore,
  } = rootStore;

  const t = useI18n();
  const [form] = Form.useForm();
  const render = renderStore.render;
  const renderStyle = renderStore.renderStyle;
  const singleImage = selectionStore.singleImage;
  const historyPlugins = renderStore.historyPlugins;
  const image = singleImage?.image;
  const [activeTool, setActiveTool] = useState<SelectionTool>(
    SelectionTool.Rectangle
  );
  const [prompt, setPrompt] = useState("");
  const [brushWidth, setBrushWidth] = useState(50);
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();

  const imageOptions = selectionStore.singleImage?.options;
  const objectLoading = imageOptions?._loading_;
  const imageStatus = imageOptions?._custom_data_history_?.imageStatus;

  const brushes = useRef({
    [SelectionTool.Rectangle]: null as null | RectBrush,
    [SelectionTool.Lasso]: null as null | LassoBrush,
    [SelectionTool.Swipe]: null as null | PathBrush,
  });

  const getBrush = (key: SelectionTool) => {
    if (!render || !image) {
      return;
    }

    const brush = brushes.current[key];
    if (brush) {
      return brush;
    }

    switch (key) {
      case SelectionTool.Rectangle: {
        const brush = (brushes.current[SelectionTool.Rectangle] = new RectBrush(
          render._FC,
          {
            ...textEditBrushOptions,
            targetElement: image as IImage,
          }
        ));
        return brush;
      }

      case SelectionTool.Lasso: {
        const brush = (brushes.current[SelectionTool.Lasso] = new LassoBrush(
          render._FC,
          {
            ...textEditBrushOptions,
            strokeDashArray: [5, 5],
            targetElement: image as IImage,
          }
        ));
        return brush;
      }

      case SelectionTool.Swipe: {
        const brush = (brushes.current[SelectionTool.Swipe] = new PathBrush(
          render._FC,
          {
            ...textEditBrushOptions,
            width: brushWidth,
            targetElement: image as IImage,
            eraseShapeColor: "#FA99FF",
            eraseShapeOpacity: 0.3,
          }
        ));
        return brush;
      }
    }
  };

  const destroyBrushes = () => {
    Object.keys(brushes.current).forEach((key) => {
      brushes.current[key as SelectionTool]?.destroy();
      brushes.current[key as SelectionTool] = null;
    });
  };

  const [hasArea, setHasArea] = useState(false);

  // 将图形上记录的涂抹区与提示词同步到表单
  const syncPromptList = () => {
    const brush = render?._FC.freeDrawingBrush || getBrush(SelectionTool.Swipe);
    const image = singleImage?.image;
    if (!brush || !image || !render) {
      return;
    }
    const options = getElementOptions.call(render, image);
    const promptMap = new Map<string, string>(
      options._custom_data_?.editTextContentList?.map((p: any) => [
        p.id,
        p.text,
      ])
    );

    const nextContentList = brush.getMasks().map((mask) => {
      const id = mask._id_;
      const index = mask.get("index");
      return {
        id,
        index,
        text: promptMap.get(id) ?? "",
      };
    });

    nextContentList.sort((c1, c2) => c1.index - c2.index);

    image.set({
      _custom_data_: {
        ...options._custom_data_,
        editTextContentList: nextContentList,
      },
    });

    setPrompt(nextContentList[0]?.text ?? "");
  };

  /**
   * 处理涂抹区状态同步
   * 1. 涂抹区变化时 更新hasArea
   * 2. 涂抹区变化时 更新表单
   */
  useEffect(() => {
    if (!render) {
      return () => {
        destroyBrushes();
      };
    }

    const handleMaskChange = () => {
      if (!render || !image) {
        return;
      }

      const brush = getBrush(SelectionTool.Swipe) as PathBrush;
      if (!brush) {
        return;
      }

      setHasArea(brush.getMaskCount() > 0);
      syncPromptList();
    };

    handleMaskChange();

    render._FC.on({
      "mask:rect:created": handleMaskChange,
      "mask:path:created": handleMaskChange,
      "mask:lasso:created": handleMaskChange,
      "mask:rect:deleted": handleMaskChange,
      "mask:path:deleted": handleMaskChange,
      "mask:lasso:deleted": handleMaskChange,
    });

    return () => {
      render._FC.off({
        "mask:rect:created": handleMaskChange,
        "mask:path:created": handleMaskChange,
        "mask:lasso:created": handleMaskChange,
        "mask:rect:deleted": handleMaskChange,
        "mask:path:deleted": handleMaskChange,
        "mask:lasso:deleted": handleMaskChange,
      });
      destroyBrushes();
    };
  }, []);

  useEffect(() => {
    if (!render || !image) {
      return;
    }

    const brush = getBrush(activeTool);
    if (!brush) {
      return;
    }

    render._FC.isDrawingMode = true;
    render._FC.freeDrawingBrush = brush;
    return () => {
      render._FC.isDrawingMode = false;
      render._FC.freeDrawingBrush = undefined;
    };
  }, [render, activeTool]);

  useEffect(() => {
    const getCursor = (activeKey: SelectionTool) => {
      switch (activeKey) {
        case SelectionTool.Rectangle: {
          return RenderMaskCursor.rectPlus;
        }
        case SelectionTool.Lasso: {
          return RenderMaskCursor.lassoPlus;
        }
        case SelectionTool.Swipe: {
          return RenderMaskCursor.pathPlus(brushWidth);
        }
      }
      return "default";
    };

    if (!render || !renderStyle) {
      return;
    }

    const cursor = getCursor(activeTool);
    render._FC.freeDrawingCursor = cursor;
    renderStyle?.setCursorStyle({
      mousedown: cursor,
      move: cursor,
      hover: cursor,
      defaults: cursor,
    });
  }, [render, renderStyle, activeTool, brushWidth]);

  // 选区个数提示
  useEffect(() => {
    if (!render) {
      return;
    }
    const handleExceedMaskLimit = (payload: { type: WarningType }) => {
      if (payload.type !== WarningType.MASK_MAX_COUNT) {
        return;
      }

      toast.show({
        type: "info",
        title: t("text-edit.exceed-mask-limit"),
        duration: 3000,
      });
    };

    render._FC.on("warning", handleExceedMaskLimit);

    return () => {
      render._FC.off("warning", handleExceedMaskLimit);
    };
  }, [render]);

  const [price, setPrice] = useState(null as null | number);
  const batchSize = Form.useWatch("batchSize", form);
  const functionBody = useMemo(() => {
    if (!batchSize) {
      return;
    }

    return JSON.stringify(toSnakeCase({ batchSize }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [batchSize]);
  useEffect(() => {
    if (!functionBody) {
      return;
    }

    const abortController = new AbortController();
    fetchPriceDesc({
      functionCode: FunctionCode.aiPosterTextEditing,
      functionBody: functionBody || "",
    }).then((res) => {
      if (abortController.signal.aborted) {
        return;
      }

      setPrice(res.amount);
    });

    return () => {
      abortController.abort();
    };
  }, [functionBody]);

  const submitDisabled =
    !image || objectLoading || imageStatus === AIPoster.ImageStatus.AuditFailed;

  const [submitLoading, setSubmitLoading] = useState(false);
  const handleCommitForm = async () => {
    if (submitLoading || submitDisabled) {
      return;
    }

    const values = form.getFieldsValue();

    const render = renderStore.render;
    const brush = render?._FC.freeDrawingBrush;
    const image = singleImage?.image;
    const imageOptions = singleImage?.options;
    const projectId = projectsStore.activeProjectId;
    if (!render || !brush || !image || !projectId || !imageOptions) {
      return;
    }

    const masks = brush.getMasks();
    if (!masks.length) {
      return toast.error(t("text-edit.select-area-tips"));
    }

    if (!prompt) {
      return toast.error(t("text-edit.no-prompt-tips"));
    }

    const isBlank = !runes(prompt).filter((c) => c !== " " && c !== "\n")
      .length;

    if (isBlank) {
      return toast.error(t("text-edit.empty-prompt-tips"));
    }

    // 图形loading
    const loadingOperation = historyPlugins?.baseAction.getLoadOperation([
      image,
    ]);

    // 图形开始loading
    const startLoading = () => {
      render.Actions.setLoading({
        type: LoadingType.FADE_IN_TO_OUT,
        text: "Generating...",
        id: imageOptions._id_,
      });

      setSubmitLoading(true);
    };

    // 图形loading结束
    const loaded = () => {
      render.Actions.setLoaded(imageOptions._id_);
      setSubmitLoading(false);
    };

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== image
      ) {
        return;
      }

      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);

    startLoading();

    const upload = createPureUpload();

    try {
      const width = Math.round(image.getScaledWidth());
      const height = Math.round(image.getScaledHeight());

      // 1. 导出画布上的mask
      const containerName = textEditBrushOptions.shapeContainerName;
      const masksBlob = await brush.exportShapesToBlob({
        isMerge: false,
        ext: "jpeg",
        shapeFill: "#fff",
        backgroundFill: "#000000",
        exportContainerType: containerName, // 导出容器类型
      });
      const beforeData =
        historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
      render._FC.freeDrawingBrush?.clearTargetElement();
      const afterData =
        historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
      const operation = historyPlugins?.baseAction.getModifiedOperation({
        beforeData,
        afterData,
      });
      abortController.signal.throwIfAborted();
      if (!masksBlob) {
        return;
      }

      if (!operation) return;
      historyPlugins?.submit(operation);
      loadingOperation && historyPlugins?.submit(loadingOperation);
      // 退出改字
      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);

      // 2. 导出画布上原图的blob
      const clippedInitImageBlob = await render.Actions.exportElementToBlob(
        image as Group,
        {
          includeType: [ElementName.IMAGE, ElementName.TEXT],
          exportType: "png",
          multiplier: 1,
        }
      );
      abortController.signal.throwIfAborted();
      if (!clippedInitImageBlob) {
        return;
      }

      // 3. 上传原图
      const initRes = await upload({
        file: await redrawImage(clippedInitImageBlob, { width, height }),
      });
      abortController.signal.throwIfAborted();
      const clippedInitImageUrl = initRes.result;
      if (!clippedInitImageUrl?.previewUrl) {
        return;
      }

      const indexMap = masks.map((mask) => mask.get("index") - 1);

      // 4. 上传mask图
      const uploadContext = await Promise.all(
        masksBlob.map((blob) => {
          return redrawImage(blob, { width, height }).then((blob) =>
            upload({ file: blob })
          );
        })
      );

      const maskUrls = uploadContext.map(
        (context) => context.result?.url ?? ""
      );

      const params: Submit.TextEditParams = {
        batchSize: values.batchSize,
        initImage: clippedInitImageUrl.previewUrl,
        mediaList: [{ maskImage: maskUrls[0], text: prompt }],
      };

      const res = await submitTask({
        projectId,
        params,
        taskCategory: AIPoster.TaskCategory.TextEdit,
      });
      editorStatusStore.addActiveTask(res.id);
      const msgId = res.id;

      // 更新美豆
      userStore.refreshMtBeanBalance();

      await dispatch({
        msgId,
        rootStore,
        shape: image,
        abortController,
        t,
      }).finally(() => {
        // 更新美豆
        userStore.refreshMtBeanBalance();
      });
    } catch (e: any) {
      if (process.env.NODE_ENV === "development") {
        console.warn("改字", e);
      }

      if (abortController.signal.aborted) {
        return;
      }

      if (uploadErrorhandler(e, t)) {
        return;
      }

      if (
        handleSubmitError(e, {
          userStore: userStore,
          openMeiDouRecordsPopup,
        })
      ) {
        return;
      }
    } finally {
      loaded();
      // 更新美豆
      userStore.refreshMtBeanBalance();
    }
  };
  const handleClickCancelArea = () => {
    if (!render?._FC || !image || !hasArea) return;
    const beforeData =
      historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
    getBrush(SelectionTool.Swipe)?.clearTargetElement();
    render?._FC.requestRenderAll();
    const afterData =
      historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
    const operation = historyPlugins?.baseAction.getModifiedOperation({
      beforeData,
      afterData,
    });
    if (!operation) return;
    historyPlugins?.submit(operation);
  };

  return createPortal(
    <AreaSelectionPanel
      prompt={prompt}
      promptPlaceholder={t("text-edit.edit-content-placeholder")}
      onPromptChange={(prompt) => {
        if (!singleImage?.image || !render) {
          return;
        }

        setPrompt(prompt);

        const options = getElementOptions.call(render, singleImage.image);
        const prevList = options._custom_data_.editTextContentList;
        singleImage.image.set({
          _custom_data_: {
            ...options._custom_data_,
            editTextContentList: prevList && [
              {
                ...prevList[0],
                text: prompt,
              },
              ...prevList.slice(1),
            ],
          },
        });
      }}
      activeTool={activeTool}
      onActiveToolChange={setActiveTool}
      tools={[
        {
          tookKey: SelectionTool.Rectangle,
          renderLabel: () => {
            return <SelectionBoxBold className="radio-btn-icon" />;
          },
        },
        {
          tookKey: SelectionTool.Lasso,
          renderLabel: () => {
            return <SelectionLassoBold className="radio-btn-icon" />;
          },
        },
        {
          tookKey: SelectionTool.Swipe,
          renderLabel() {
            return <PaintBold className="radio-btn-icon" />;
          },
          renderParams() {
            const handleBrushWidthChange = (value: number) => {
              setBrushWidth(value);
              const brush = getBrush(SelectionTool.Swipe) as null | PathBrush;
              brush?.setWidth(value);
            };
            return (
              <PaintSize value={brushWidth} onChange={handleBrushWidthChange} />
            );
          },
        },
      ]}
      form={{
        props: {
          form,
          initialValues: {
            batchSize: 4,
          },
        },
        renderFormItems() {
          return (
            <div className={styles.form}>
              <div className="batch-size">
                <div className="batch-size-label">
                  <strong>{t("Batch Size")}</strong>
                </div>
                <QuantitySection />
              </div>
            </div>
          );
        },
      }}
      renderCancelSelectionButton={() => {
        return (
          <CancelAreaButton
            disabled={!hasArea}
            onClick={handleClickCancelArea}
          />
        );
      }}
      renderSubmitButton={() => {
        return (
          <SubmitButton
            price={price}
            disabled={submitDisabled}
            onClick={handleCommitForm}
          />
        );
      }}
    />,
    document.getElementById(editorLayoutContent)!
  );
}

export default observer(TextEditPanel);
