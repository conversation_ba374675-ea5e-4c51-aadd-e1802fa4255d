@import '../header-common.scss';

.actions {
  display: flex;
  gap: 14px;
  padding: 8px 12px;

  :global {
    .actions-item {
      @include header-button-mixin;

      flex-direction: column;
      width: auto;

      .icon-box {
        width: 28px;
        height: 28px;
        min-height: 28px;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        flex-grow: 0;

        svg {
          width: 17.5px;
          height: 17.5px;
        }
      }

      .icon-desc {
        margin-top: 2px;
        color: $system-header-secondary;
        font-family: "PingFang SC";
        font-size: 10px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }

      &:hover:not(:global(.disabled)):not(:global(.selected)) {
        background: none;
        box-shadow: none;

        .icon-box {
          background: $system-header-thirdary;
          // box-shadow: 0px 0px 0px 1px var(--system-stroke-button, #323B48) inset;

          // svg {
          //   color: #fff;
          // }
        }
      }

      &.selected {
        background: none;

        .icon-box {
          background: $system-background-secondary;

          svg {
            color: $system-header-secondary;
          }
        }
      }
    }
  }
}

.actionsItemTips {
  &:global(.ant-tooltip) {
    // width: 280px;
    height: 56px;
    max-width: max-content;
    border-radius: var(--radius-12, 12px);
    border: 1px solid #FFF;
    background: rgba(255, 255, 255, 0.90);
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 24px 128px 0px rgba(0, 0, 0, 0.16);
    backdrop-filter: blur(14px);
    padding: 16px;

    :global {
      .ant-tooltip-content {
        background: transparent;
      }

      .ant-tooltip-inner {
        background: transparent;
        padding: 0;
        color: var(--system-content-thirdary, #6A7B94);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
        min-height: auto;
        box-shadow: none;
      }
    }

    .actionsItemTipsContent {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // gap: 12px;

      .title {
        color: var(--system-content-secondary, #293545);
        margin-right: 46px;
      }

      .price {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 2px;

        svg {
          width: 16px;
          height: 16px;
          color: #FBCD6F;
        }
      }
    }
  }
}