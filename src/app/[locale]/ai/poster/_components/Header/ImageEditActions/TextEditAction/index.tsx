import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>enerate } from "@meitu/candy-icons";
import { Tooltip } from "antd";
import classNames from "classnames";
import { observer } from "mobx-react";
import { useRootStore } from "@/app/[locale]/ai/poster/_store";
import { HeaderAction } from "@/app/[locale]/ai/poster/_store/headerAction";
import { AIPoster } from "@/api/types/aiPoster/task";
import { Fragment, useEffect, useState } from "react";
import { isTextEditing } from "@/app/[locale]/ai/poster/_utils/isTextEditing";
import { useI18n } from "@/locales/client";
import { trackEvent } from "@meitu/subscribe-intl";
import { Track } from "@/app/[locale]/ai/poster/_constant/track";
import TextEditPanel from "./TextEditPanel";
import { useEnsureSelectSingleImage } from "@/app/[locale]/ai/poster/_hooks/useEnsureSelectSingleImage";
import styles from "../index.module.scss";
import { FunctionCode, MeiDouFetchPriceDescResponse } from "@/api/types/meidou";
import { fetchPriceDesc } from "@/api/meidou";

function TextEditAction() {
  const t = useI18n();
  const rootStore = useRootStore();
  const { headerActionStore, renderStore } = rootStore;

  const { singleImage } = useEnsureSelectSingleImage();

  const image = singleImage?.image;
  const imageOptions = singleImage?.options;
  const objectLoading = imageOptions?._loading_;
  const imageStatus = imageOptions?._custom_data_history_?.imageStatus;
  const disabled =
    !image || objectLoading || imageStatus === AIPoster.ImageStatus.AuditFailed;
  const [price, setPrice] = useState<MeiDouFetchPriceDescResponse | null>(null);

  //快捷键注册
  useEffect(() => {
    const renderHotkey = renderStore.renderHotkey;
    if (!renderHotkey || disabled) return;

    const hotkeyHandler = (event: KeyboardEvent) => {
      if (isTextEditing(renderStore.render)) {
        return;
      }
      event.preventDefault();
      headerActionStore.setActiveHeaderAction(HeaderAction.ModifyText);
    };

    renderHotkey.registerHotKey("shift + t", hotkeyHandler);

    return () => {
      renderHotkey.unregisterHotKey("shift + t", hotkeyHandler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [renderStore.renderHotkey, disabled]);

  // 获取美豆数量
  useEffect(() => {
    if (!image) {
      return;
    }

    let ignore = false;

    fetchPriceDesc({
      functionCode: FunctionCode.aiPosterTextEditing,
      functionBody: JSON.stringify({ batch_size: 1 }),
    }).then((res) => {
      if (ignore) {
        return;
      }

      setPrice(res);
    });

    return () => {
      ignore = true;
    };
  }, [image]);

  if (!image || !imageOptions) {
    return null;
  }

  const isActive =
    headerActionStore.activeHeaderAction === HeaderAction.ModifyText;

  const handleActiveTextEdit = () => {
    if (disabled) {
      return;
    }

    trackEvent("edit_page_top_function_click", {
      function: Track.FunctionEnum.ModifyText,
    });

    if (headerActionStore.activeHeaderAction !== HeaderAction.ModifyText) {
      headerActionStore.setActiveHeaderAction(HeaderAction.ModifyText);
    } else {
      headerActionStore.activateActionAndResetCursor();
    }
  };

  return (
    <Fragment>
      <Tooltip
        arrow={false}
        rootClassName={styles.actionsItemTips}
        align={{
          offset: [0, -15],
        }}
        title={
          <div className={styles.actionsItemTipsContent}>
            <span className={styles.title}>{t("Image Text Editor")}</span>
            {price?.amount === 0 ? (
              <div>
                限免权益生效中，剩余{" "}
                <span style={{ color: "#FFB200" }}>{price?.totalFreeNum}</span>{" "}
                次
              </div>
            ) : (
              <div className={styles.price}>
                <span> 消耗 </span>
                <MtBeanFill />
                <span style={{ color: "#293545" }}>{price?.amount}</span>
              </div>
            )}
          </div>
        }
      >
        <div
          className={classNames("actions-item", isActive && "selected", {
            disabled,
          })}
          onClick={handleActiveTextEdit}
        >
          <div className="icon-box">
            <TexGenerate />
          </div>
          <span className="icon-desc">{t("Image Text Editor")}</span>
        </div>
      </Tooltip>

      {isActive && <TextEditPanel />}
    </Fragment>
  );
}

export default observer(TextEditAction);
