import { observer } from 'mobx-react';
import { useRootStore } from '../../../_store';
import { useStore } from '@/contexts/StoreContext';
import { FrameElementOptions, getElementOptions } from '@meitu/whee-infinite-canvas';
import { Fragment } from 'react';
import { UpdateTemplate } from '@/api/aiPoster/template';
import toast from '@/components/Toast';


function Template() {
    const rootStore = useRootStore();
    const { userStore, posterConfigStore } = useStore();
    const renderStore = rootStore.renderStore;
    const { selectionStore, paramsEditorStore  } = rootStore;
    const selectedObjects = selectionStore.objectsOptions;
    const selectedLength = selectedObjects.length === 1;
    const isFrame = selectedObjects[0]?._name_ === "frame";
    const isTemplate = selectedObjects?.[0]?._custom_data_history_?.template?.id;

    const currentTemplate = selectionStore.activeObjects[0];

    const fixedTemplateLoadingKey = "fixedTemplateLoadingKey";
    const isWhiteUser = posterConfigStore.config?.generateTemplatePermission;
    // console.log(isFrame, isTemplate, selectedLength, isWhiteUser)
    if (!(isFrame && isTemplate && selectedLength && isWhiteUser)) return null;
    const getOptions = async () => {
        if (!renderStore.render?._FC) return
            const data = getElementOptions.call(renderStore.render, currentTemplate) as FrameElementOptions;
            const fn = (children: any) => {
                children.forEach((item: any) => {
                    if (item._name_ === "text") {
                        const targetFontUrl = paramsEditorStore.familyList.find(font => font.defaultName === item.fontFamily || font.name === item.fontFamily)
                        item._font_url_ = item._font_url_ || targetFontUrl?.wofFontFile;
                    }
                    if (item.children) {
                        fn(item.children);
                    }
                });
            }
        if (data?.children) {
            fn(data.children as any[]);
        }
        const { result } = await UpdateTemplate({
            template_id: currentTemplate._custom_data_history_.template.id,
            config: {
                data,
                v2: true
            }
        })
        if (result) {
            toast.destroy(fixedTemplateLoadingKey);
            toast.show({
                title: "修复成功",
                type: "success",
            });
        } else {
            toast.destroy(fixedTemplateLoadingKey);
            toast.show({
                title: "修复失败",
                type: "error",
            });
        }
    }

    return (
        <Fragment>
            <button
                onClick={getOptions}
                className="rounded-full mr-2 bg-gray-950 px-2.5 py-0.5 text-sm/6 font-medium text-white focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-950">
                修复字体⚠️（谨慎使用）
            </button>
        </Fragment>
    );
}
export default observer(Template);