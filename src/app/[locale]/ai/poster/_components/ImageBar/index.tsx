import React, { useState, useEffect } from "react";
import { observer } from "mobx-react";
import { Image } from "antd";
import { ExclamationmarkTriangle } from "@meitu/candy-icons";
import { queryTask } from "@/api/aiPoster/task";
import "./index.scss";
import { createImage, Group, ImageOptions } from "@meitu/whee-infinite-canvas";
import { useRootStore } from "../../_store";
import { useSaveCanvas } from "../../_hooks/useSaveProjectHistory";
import { getInitWatermarkImage } from "../../_utils/getInitWatermarkImage";
import { Loading } from "@/components";
import { useI18n } from "@/locales/client";
import { DraftType } from "@/api/types/editorConfig";
import { AIPoster } from "@/api/types/aiPoster/task";

export interface ImageBarProps {}

const renderNull = () => {
  return <></>;
};

/**
 * 图片状态
 */
export enum ThumbnailImageStatus {
  /**
   * 正常
   */
  Success = 1,
  /**
   * 审核不通过
   */
  AuditFailed = 2,
  /**
   * 加载中
   */
  Loading = 3,
}

const singleImageWidth = 72; // 单张图片的宽度

const ImageBar: React.FC<ImageBarProps> = (props) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [historyImage, setHistoryImage] = useState<any[]>([]);

  const rootStore = useRootStore();
  const { selectionStore, projectsStore, renderStore } = rootStore;
  const { submitSaveElements } = useSaveCanvas(rootStore);

  const { activeObjects, objectsOptions } = selectionStore;
  const { activeProjectId } = projectsStore;

  const t = useI18n();

  useEffect(() => {
    renderImageBar();
  }, [objectsOptions?.[0]?._custom_data_history_?.isHaveCompletedTaskId]);

  const renderImageBar = async () => {
    /**
     * 抠图和超清不展示缩略图
     */
    //选中非单个元素
    if (objectsOptions.length !== 1) {
      return setHistoryImage([]);
    }
    const activeOption = objectsOptions[0];

    //无项目id
    if (!activeProjectId) {
      return setHistoryImage([]);
    }
    //任务已完成
    const isHaveCompletedTaskId =
      activeOption._custom_data_history_?.isHaveCompletedTaskId;
    if (!isHaveCompletedTaskId) {
      return setHistoryImage([]);
    }

    try {
      const res = await queryTask({
        ids: [isHaveCompletedTaskId],
        projectId: activeProjectId,
      });
      const data = res[0];
      if (!data) return;
      if (data.resultImages.length > 0) {
        // 海报生图不需要原图
        setHistoryImageList(
          data.resultImages,
          data.taskCategory === AIPoster.TaskCategory.PosterImage
            ? ""
            : data?.initImage?.[0]
        );
      }
    } catch (error) {
      // console.log(error, 'error-----');
    }
    // }
  };

  const setHistoryImageList = (images: any[], initImage?: string) => {
    const newImages = [...images];
    const activeOption = objectsOptions[0];

    //如果存在原图 ，将原图拼接首个缩略位置
    // const initImage = getInitWatermarkImage(activeOption);
    if (initImage) {
      newImages.unshift({
        watermarkUrl: initImage,
        urlSign: initImage,
        imageStatus: ThumbnailImageStatus.Success,
        isOrigin: true,
      });
    }
    // console.log(activeOption, 'activeOption-------重新组装数据了-');

    setHistoryImage(newImages);
    setFirstSuccessActiveImage(newImages);
  };

  const setFirstSuccessActiveImage = (image: any) => {
    const activeOption = objectsOptions[0];
    // 取最原始的链接不带？后面的参数
    let activeImageUrl = (activeOption as ImageOptions)?.src.split("?")[0];

    let _activeIndex = image.findIndex(
      (v: any) => v.watermarkUrl?.split("?")[0] === activeImageUrl
    );
    if (_activeIndex !== -1) {
      setActiveIndex(_activeIndex);
      return;
    }

    // if(activeOption?._custom_data_history_?.urlShort)
    const index = image.findIndex(
      (v: any) => v.imageStatus === ThumbnailImageStatus.Success && !v.isOrigin
    );

    if (index === -1) {
      setActiveIndex(0);
      return;
    }
    setActiveIndex(index);
  };

  //历史切换图片 选中对应的index
  useEffect(() => {
    if (objectsOptions.length > 0) {
      const activeOption = objectsOptions[0];
      // 取最原始的链接不带？后面的参数
      const src = (activeOption as ImageOptions)?._custom_data_history_
        .urlShort;
      if (src) {
        // kt参数会改变
        const index = historyImage.findIndex((v: any) => v.urlShort === src);

        if (index !== -1) {
          setActiveIndex(index);
        }
      }
    }
  }, [(objectsOptions?.[0] as ImageOptions)?.src]);

  // // 是否是原图
  // const isInitImageBySrc = (image: any) => {
  //   const shortSrc = removeParams(image.watermarkUrl);
  //   const activeOption = objectsOptions[0];
  //   const initImage = getInitWatermarkImage(activeOption);
  //   const shortInitSrc = initImage && removeParams(initImage);

  //   return shortInitSrc && shortInitSrc === shortSrc;
  // };

  // // 获取原图的生图参数
  // const getInitImageParams = async (image: any) => {
  //   const activeOption = objectsOptions[0];
  //   let params = {};

  //   const linkMsgId = activeOption?._custom_data_history_?.params?.linkMsgId;
  //   const res = await queryTask({
  //     ids: [linkMsgId],
  //     projectId: activeProjectId
  //   });
  //   const data = res[0];
  //   if (!data) return;
  //   params = data.params;

  //   return params;
  // };

  const handleClick = async (image: any, index: number) => {
    if (image.imageStatus !== ThumbnailImageStatus.Success) return;
    if (index === activeIndex) return;
    setActiveIndex(index);
    const beforeData =
      renderStore?.historyPlugins?.baseAction.getElementData({
        target: activeObjects[0],
      }) ?? [];

    const shape = await createImage('', { src: image.urlSign });

    await renderStore.render?.Actions.replaceImage(activeObjects[0] as Group, {
      src: image.urlSign,
      width: shape.width,
      height: shape.height,
      scaleX: 1,
      scaleY: 1,
      _custom_data_history_: {
        ...activeObjects?.[0]?._custom_data_history_,
        imageStatus: image.imageStatus,
        urlShort: image.urlShort,
      },
    });

    shape.dispose();

    const afterData =
      renderStore?.historyPlugins?.baseAction.getElementData({
        target: activeObjects[0],
      }) ?? [];
    const operation =
      renderStore?.historyPlugins?.baseAction.getModifiedOperation({
        beforeData,
        afterData,
      });
    if (operation) {
      renderStore?.historyPlugins?.submit(operation);
      submitSaveElements([activeObjects[0]]);
    }

    //  fix: 修复切换图片后，图片位置不对的问题
    if (!renderStore?.render?.isElementInViewport(activeObjects[0])) {
      renderStore?.render?.backToOriginPosition();
    }
  };

  /**
   *
   * @param historyImage
   *
   * @description 根据图片数量设置宽度
   * 每张图看度是61px, 一张图是61 * 1 + 20px - (0 * 12px)
   * 两张图 是61 * 2 + 20px -(1 * 12px)
   * 三张图是61 * 3 + 20px - (2 * 12px)
   * 以此类推
   */

  const handleWidth = (historyImage: any) => {
    let width = 0;
    if (historyImage.length > 0) {
      width =
        singleImageWidth * historyImage.length +
        20 -
        (historyImage.length - 1) * 12;
    }
    return width;
  };

  const renderImageErr = () => {
    return (
      <div className="image-bar-item-error">
        <ExclamationmarkTriangle
          style={{
            color: "#fff",
          }}
        />
        <div className="image-bar-item-error-text">
          {t("Image generate failed")}
        </div>
      </div>
    );
  };
  //   console.log(activeIndex, 'activeIndex');

  return historyImage.length > 0 ? (
    <>
      <div
        className={`editor-image-bar`}
        style={{ width: handleWidth(historyImage) }}
      >
        {historyImage.map((image: any, index: number) => (
          <div
            key={index}
            className={`image-bar-item ${
              activeIndex === index ? "active" : ""
            } image-bar-item${index + 1}`}
            onClick={() => {
              handleClick(image, index);
            }}
          >
            {index === 0 && image.isOrigin ? (
              <div className="origin-tag">{t("Original")}</div>
            ) : (
              <></>
            )}
            {/* <img src={image.watermarkUrl} alt="" /> */}
            {image.imageStatus === ThumbnailImageStatus.Loading ? (
              <Loading className="poster-editor-image-loading" />
            ) : image.imageStatus === ThumbnailImageStatus.AuditFailed ? (
              renderImageErr()
            ) : (
              <Image
                src={
                  image.urlSign
                  // &&
                  // toAtlasImageView2URL(image.watermarkUrl, {
                  //   width: 60,
                  //   mode: 2
                  // })
                }
                placeholder={
                  <Loading className="poster-editor-image-loading" />
                }
                alt=""
                preview={false}
                loading="lazy"
              ></Image>
            )}
          </div>
        ))}
      </div>
    </>
  ) : (
    renderNull()
  );
};

export default observer(ImageBar);
