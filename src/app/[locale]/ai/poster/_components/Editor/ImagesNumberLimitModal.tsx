"use client"

import { <PERSON><PERSON>, <PERSON><PERSON> } from 'antd';
import { observer } from 'mobx-react';
import styles from './index.module.scss';
import { CrossBlack } from '@meitu/candy-icons';
import classNames from 'classnames';
import { useState } from 'react';
import { useCreateProjectInEditor } from '@/app/[locale]/ai/poster/_hooks/useCreateProjectInEditor';
import buttonStyles from '@/styles/button.module.scss';
import { useI18n } from '@/locales/client';

type ModalDescriptor = {
  opened: boolean;
};

type ModalDescriptorMap = {
  [uid in number]: {
    [projectId in number]: ModalDescriptor;
  };
};

const modalDescriptorKey = 'poster-editor/images-limit-modal';

function getModalDescriptorMap() {
  const descriptorStr = localStorage.getItem(modalDescriptorKey);
  if (!descriptorStr) {
    return;
  }

  let descriptor: null | ModalDescriptorMap = null;
  try {
    descriptor = JSON.parse(descriptorStr) as ModalDescriptorMap;
  } catch (e) {
    if (process.env.NODE_ENV === 'development') {
      console.error('JSON解析失败', e);
      return;
    }
  }

  return descriptor;
}

function getModalDescriptor(uid: number, projectId: number) {
  const descriptorMap = getModalDescriptorMap();
  if (!descriptorMap) {
    return;
  }

  return descriptorMap[uid]?.[projectId];
}

function setModalDescriptor(
  uid: number,
  projectId: number,
  descriptor: ModalDescriptor
) {
  const descriptorMap = getModalDescriptorMap() ?? {};

  const userModalMap = descriptorMap[uid] ?? {};

  descriptorMap[uid] = userModalMap;

  userModalMap[projectId] = descriptor;

  try {
    const descriptorMapStr = JSON.stringify(descriptorMap);
    localStorage.setItem(modalDescriptorKey, descriptorMapStr);
  } catch (e) {
    if (process.env.NODE_ENV !== 'development') {
      console.error(e);
    }
  }
}

export type UseImagesLimitModalDeps = {
  uid?: number | null;
  projectId?: number;
};

export function useImagesNumberLimitModal({
  uid,
  projectId
}: UseImagesLimitModalDeps) {
  const [open, setOpen] = useState(false);

  const openModal = () => {
    if (!uid || !projectId) {
      return;
    }

    const descriptor = getModalDescriptor(uid, projectId);
    if (descriptor?.opened) {
      return;
    }

    setOpen(true);
  };

  const markOpened = () => {
    if (!uid || !projectId) {
      return;
    }

    setModalDescriptor(uid, projectId, { opened: true });
  };

  const closeModal = () => {
    markOpened();
    setOpen(false);
  };

  return {
    open,
    openModal,
    closeModal
  };
}

type ImagesNumberLimitModalProps = {
  open?: boolean;

  onClose: () => void;
};

function ImagesNumberLimitModal({
  open,
  onClose
}: ImagesNumberLimitModalProps) {
  const t = useI18n();

  const [loading, setLoading] = useState(false);
  const { createProject } = useCreateProjectInEditor();

  const handleCreateNew = () => {
    if (loading) {
      return;
    }

    setLoading(true);
    createProject().finally(() => {
      setLoading(false);
      onClose();
    });
  };

  return (
    <>
      <Modal
        open={open}
        rootClassName="override-ant"
        className={classNames(styles['images-number-limit-modal'], 'override-ant')}
        width={300}
        footer={null}
        maskClosable={false}
        closeIcon={<CrossBlack />}
        onCancel={() => {
          onClose();
        }}
        centered
        destroyOnClose
      >
        <div className="modal-content">
          <div className="title">
            {t("The current canvas is quite crowded with images, which may affect your creative experience! We suggest you start with a new canvas.")}
          </div>
          <div className="button-box">
            <button
              className={classNames('btn', 'cancel', buttonStyles.secondary)}
              onClick={() => onClose?.()}
            >
              {t("Continue anyway")}
            </button>
            <Button
              loading={loading}
              className={classNames('btn', 'confirm', buttonStyles.main)}
              onClick={() => {
                handleCreateNew();
              }}
            >
              {t("New")}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default observer(ImagesNumberLimitModal);
