import { Modal, Image } from "antd";
import { observer } from "mobx-react";
import styles from "./index.module.scss";
import { useEffect, useState } from "react";
import { CrossBlack } from "@meitu/candy-icons";
import { useStore } from "@/contexts/StoreContext";
import { useRootStore } from "../../../_store";
import {
  fetchCreateModelGuide,
  ModelGuideListItem,
} from "@/api/aiPoster/guide";
import { DraftType } from "@/api/types/editorConfig";
import { Loading } from "@/components";
import { useI18n } from "@/locales/client";

const CreateTipModalKey = "poster-editor/create-tip-modal";

type ModalDescriptionMap = {
  [uid in number]: boolean;
};

// 获取创建弹窗的存储
function getCreateStorageMap() {
  const descriptionStr = localStorage.getItem(CreateTipModalKey);
  if (!descriptionStr) {
    return;
  }

  let description: null | ModalDescriptionMap = null;
  try {
    description = JSON.parse(descriptionStr) as ModalDescriptionMap;
  } catch (e) {
    if (process.env.NODE_ENV === "development") {
      console.error("JSON解析失败", e);
      return;
    }
  }

  return description;
}

function CreateTipModal() {
  const t = useI18n();
  const { editorStatusStore } = useRootStore();
  const { userStore } = useStore();
  const uid = userStore.UID;

  const [openModal, setOpenModal] = useState(false);
  const [content, setContent] = useState<ModelGuideListItem>();

  useEffect(() => {
    if (!uid) {
      return;
    }

    const descriptionMap = getCreateStorageMap() ?? {};
    // 当前uid没有打开过
    if (descriptionMap[uid] !== true && editorStatusStore.showCreate) {
      fetchCreateModelGuide({ from: DraftType.AI_POSTER })
        .then((res) => {
          setOpenModal(true);
          // 暂不支持多条数据banner
          setContent(res?.list[0]);
        })
        .catch((e) => {
          if (process.env.NODE_ENV === "development") {
            console.error("获取弹窗内容失败", e);
          }
        });
    }
  }, [editorStatusStore.showCreate, uid]);

  const handleClose = () => {
    setOpenModal(false);
    if (!uid) {
      return;
    }
    const descriptionMap = getCreateStorageMap() ?? {};
    descriptionMap[uid] = true;
    // 存储
    localStorage.setItem(CreateTipModalKey, JSON.stringify(descriptionMap));
  };

  return (
    <Modal
      open={openModal}
      classNames={{
        wrapper: styles.createTipModal,
        mask: styles.createTipModalMask,
      }}
      width={648}
      footer={null}
      maskClosable={false}
      closeIcon={<CrossBlack />}
      onCancel={handleClose}
      centered
      destroyOnClose
    >
      <div className={styles.modalContent}>
        <div className={styles.leftBox}>
          <Image
            width={"100%"}
            style={{ borderRadius: "12px 0 0 12px" }}
            preview={false}
            src={content?.pic}
            alt={content?.title}
            placeholder={<Loading />}
          />
        </div>
        <div className={styles.rightBox}>
          <h3>{content?.title}</h3>
          <p>{content?.desc}</p>
          <div className={styles.btnBox} onClick={handleClose}>
            {t("Try now")}
          </div>
        </div>
      </div>
    </Modal>
  );
}

export default observer(CreateTipModal);
