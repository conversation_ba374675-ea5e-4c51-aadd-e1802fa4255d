@import '@/styles/variable.scss';

.button {
  min-width: 118px;
  padding: 0 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
  border-radius: var(--radius-8, 8px);
  background: $system-content-brand-primary;
  color: #fff;
  font-size: 14px;
  user-select: none;

  &:disabled {
    cursor: not-allowed;
    border-radius: var(--radius-6, 6px);
    background: rgba(20, 31, 51, 0.12) !important;
    color: #4A5564 !important;
  }

  &:hover {
    opacity: 0.8;
  }

  :global {
    .mt-bean-icon {
      margin-right: 2px;

      svg {
        margin-right: 2px;
        color: #FBCD6F;
      }
    }
  }
}