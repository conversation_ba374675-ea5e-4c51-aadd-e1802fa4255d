import classNames from "classnames";
import styles from "./index.module.scss";
import { observer } from "mobx-react-lite";
import { MtBeanFill } from "@meitu/candy-icons";
import { Spin } from "antd";
import { useI18n } from "@/locales/client";

type SubmitButtonProps = {
  onClick?: () => void;
  price?: number | null;
  disabled?: boolean;
  loading?: boolean;
};

function SubmitButton({
  onClick,
  disabled,
  price,
  loading = false,
}: SubmitButtonProps) {
  const t = useI18n();

  return (
    <Spin spinning={typeof price !== "number" || loading}>
      <button
        className={classNames(styles.button)}
        disabled={disabled}
        onClick={onClick}
      >
        {Number(price) > 0 && (
          <span className="mt-bean">
            <MtBeanFill className="mt-bean-icon" />
            <span className="mt-bean-price">{price}</span>
          </span>
        )}

        <span>{t("Generate")}</span>
      </button>
    </Spin>
  );
}

export default observer(SubmitButton);
