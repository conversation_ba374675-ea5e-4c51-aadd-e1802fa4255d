@import "@/styles/variable.scss";

.params {
  box-sizing: border-box;
  width: 100%;
  height: 44px;
  padding: 8px;
  border-radius: var(--radius-10, 10px);
  border: 1px solid #E9EBF0;
  background: #FFF;
  display: flex;
  align-items: center;
  justify-content: space-between;

  :global {
    .prompt-input {
      box-sizing: border-box;
      width: calc(100% - 48px);
      height: 20px;
      font-size: 14px;
      background: transparent;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: $system-content-secondary;

      &::placeholder {
        color: $system-content-fourth;
      }
    }

    .params-button {
      padding: 6px;
      border-radius: 8px;
      svg {
        width: 20px;
        height: 20px;
        color: $system-content-secondary;
      }

      &.ant-popover-open {
        background: $system-background-secondary;
      }
    }
  }
}

.options {
  border-radius: var(--radius-12, 12px);
  border: 1px solid #E9EBF0;
  background: #FFF;
}