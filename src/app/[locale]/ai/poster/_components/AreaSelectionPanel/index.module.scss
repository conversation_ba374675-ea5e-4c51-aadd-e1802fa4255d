@import "@/styles/variable.scss";

.panel {
  display: inline-grid;
  grid-template-columns: repeat(2, auto);
  grid-auto-rows: 44px;
  padding: 12px;
  column-gap: 12px;
  row-gap: 12px;
  position: absolute;
  left: 0;
  top: 0;

  border-radius: var(--radius-16, 16px);
  border: 1px solid #E9EBF0;
  background: #FFF;
  box-shadow: 0px 6px 24px 0px rgba(0, 0, 0, 0.06);


  :global {
    .selection-area {
      display: flex;
      min-width: 252px;
      align-items: center;
      gap: 12px;
      position: relative;
      padding-right: 12px;

      .divider {
        width: 2px;
        height: 26px;
        background: $system-stroke-divider;
      }


      &.area-mode {
        &::after {
          content: '';
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 2px;
          height: 26px;
          background: $system-stroke-divider;
        }
      }
    }

    .exit-box {
      position: absolute;
      top: 0;
      right: -28px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border-radius: var(--radius-24, 24px);
      background: var(--system-background-primary, #FFF);
      box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 6px 24px 0px rgba(0, 0, 0, 0.06);
      display: flex;
      justify-content: center;
      align-items: center;

      svg {
        width: 10px;
        height: 10px;
        color: #293545;
        cursor: pointer;
      }
    }
  }
}