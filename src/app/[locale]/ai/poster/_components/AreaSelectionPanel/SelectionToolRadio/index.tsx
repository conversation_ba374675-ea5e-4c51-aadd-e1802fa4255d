import { MinusCircleBold, MinusCircleBoldFill, PaintBold, PlusCircleBold, PlusCircleBoldFill, SelectionBoxBold, SelectionLassoBold } from "@meitu/candy-icons";
import { SelectionTool, SelectionToolDesc } from "../types";
import { useI18n } from "@/locales/client";
import classNames from "classnames";
import styles from "./index.module.scss";
import { Popover } from "antd";
import { useRef } from "react";

type SelectionToolRadioProps = {
  activeTool?: SelectionTool;
  onActiveToolChange?: (value: SelectionTool) => void;
  className?: string;

  items?: SelectionToolDesc[];
};

function getDefaultToolItems(t: ReturnType<typeof useI18n>): SelectionToolDesc[] {
  return [
    {
      tookKey: SelectionTool.Rectangle,
      renderLabel: (activeTool?: SelectionTool) => {
        return (
          <SelectionBoxBold className="radio-btn-icon"/>
        );
      }
    },
    {
      tookKey: SelectionTool.Lasso,
      renderLabel: (activeTool?: SelectionTool) => {
        return (
          <SelectionLassoBold className="radio-btn-icon" />
        );
      }
    },
    {
      tookKey: SelectionTool.Swipe,
      renderLabel: (activeTool?: SelectionTool) => {
        return (
          <PaintBold className="radio-btn-icon" />
        );
      }
    },
  ];
}

export function SelectionToolRadio({
  activeTool,
  onActiveToolChange,
  className,
  items,
}: SelectionToolRadioProps) {

  const t = useI18n();

  const tools = items || getDefaultToolItems(t);

  const containerRef = useRef<HTMLDivElement>(null);

  return(
    <div className={classNames(styles.radio, className)} ref={containerRef}>
      {
        tools.map(tool => {
          const isActive = activeTool === tool.tookKey;

          return (
            <Popover 
              key={tool.tookKey}
              open={isActive && !!tool.renderParams}
              content={tool.renderParams?.()}
              classNames={{
                body: styles.params,
              }}
              getPopupContainer={() => containerRef.current!}
            >
              <label
                className={classNames("radio-btn", {
                  "radio-btn-active": isActive,
                })}
              >
                <input
                  className="radio-btn-input"
                  type="radio"
                  name="selectionTool"
                  value={tool.tookKey}
                  checked={isActive}
                  onChange={(e) => {
                  onActiveToolChange?.(e.target.value as SelectionTool);
                  }}
                />

                {tool.renderLabel?.(activeTool)}
              </label>
            </Popover>
          )
        })
      }
    </div>
  )
}