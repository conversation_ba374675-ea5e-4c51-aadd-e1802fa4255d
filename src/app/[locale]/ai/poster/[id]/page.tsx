"use client";

import { useEffect } from "react";
import { fetchProjectDetail } from "@/api/aiPoster/project";
import { observer } from "mobx-react-lite";
import { useRouter } from "next/navigation";
import { useStore } from "@/contexts/StoreContext";
import _ from "lodash";
import { useRootStore } from "../_store";
import { useConsumeAction } from "../_hooks/consume-action/editor-project/useConsumeAction";
import { BaseRouterUrl } from "@/constants";

function Project({ params }: { params: { id: string } }) {
  const { id } = params;
  const { userStore } = useStore();
  const { projectsStore, configStore, editorStatusStore } = useRootStore();
  const consumeAction = useConsumeAction();
  configStore.config?.name;

  // const t = useI18n();

  // const { open: openQuestionModal } = useQuestionModal();
  const router = useRouter();
  const projectId = parseInt(id);
  // if (!projectId) {
  //   notFound();
  // }

  useEffect(() => {
    if (editorStatusStore.initFinish || !userStore.initFinish) {
      return;
    }

    if (!userStore.isReady) {
      router.replace(`${BaseRouterUrl}`);
      return;
    }

    let ignore = false;
    editorStatusStore.globalEnable();
    projectsStore
      .fetchProjects()
      .then(async () => {
        // 根据项目id获取图层信息
        try {
          if (Number.isNaN(projectId)) {
            throw TypeError("projectId is not a number");
          }

          return await fetchProjectDetail({ projectId });
        } catch (e) {
          // 项目id不合法
          // 找到最近编辑的项目
          if (id !== "latest" || !projectsStore.projects.length) {
            throw e;
          }
          try {
            return await fetchProjectDetail({
              projectId: projectsStore.projects[0].id,
            });
          } catch (e) {
            throw e;
          }
        }
      })
      // 选择一个项目并加载
      .then((projectDetail) => {
        if (ignore) {
          return;
        }

        return projectsStore.setActiveProject(projectDetail).then(() => {
          if (ignore) {
            return;
          }

          router.replace(`${BaseRouterUrl}/${projectDetail.projectId}`);
        });
      })
      .then(consumeAction)
      .catch((e) => {
        if (process.env.NODE_ENV === "development") {
          console.log(e);
        }

        if (ignore) {
          return;
        }

        router.replace(`${BaseRouterUrl}`);
      })
      .finally(() => {
        if (ignore) {
          return;
        }
        editorStatusStore.setInitFinish(true);
      });

    return () => {
      ignore = true;
    };
  }, [userStore.initFinish, userStore.isReady]);

  // return <SubscribeTips/>;
  return null;
}

export default observer(Project);
