import { GuideDescription } from "./types";
import assets from "./assets-index.json";
import { fillGuideElements } from "./fillGuideElements";
import { ElementName, Render } from "@meitu/whee-infinite-canvas";
import { tipsColor, titleColor } from "./constants";
import { useI18n } from "@/locales/client";

type FillHighDefinitionGuideOptions = {
  render: Render;
  t: ReturnType<typeof useI18n>;
  defaultFont: string;
};
export async function fillHighDefinitionGuide({
  render,
  t,
  defaultFont,
}: FillHighDefinitionGuideOptions) {
  const bgRemovalGuideDesc: GuideDescription = {
    title: {
      icon: {
        src: assets["high-definition"]["title-icon"],
      },
      text: {
        content: t("guide.high-definition.title"),
        fontSize: 24,
        fontFamily: defaultFont,
        color: titleColor,
      },
      gap: 12,
    },

    steps: {
      list: [
        [
          {
            type: ElementName.TEXT,
            content: t("guide.high-definition.step1"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["common"]["image-icon"],
            topOffset: 16,
          },
        ],
        [
          {
            type: ElementName.TEXT,
            content: t("guide.high-definition.step2"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["high-definition"]["hd-icon"],
            topOffset: 16,
          },
          {
            type: ElementName.IMAGE,
            src: assets["high-definition"]["preview"],
            topOffset: 16,
          },
        ],
        [
          {
            type: ElementName.TEXT,
            content: t("guide.high-definition.step3"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["common"]["download"],
            topOffset: 16,
          },
          {
            type: ElementName.IMAGE,
            src: assets["common"]["download-dropdown"],
            topOffset: 16,
          },
        ],
      ],
      gap: 48,
      topOffset: 40,
    },
  };

  return await fillGuideElements(bgRemovalGuideDesc, { render });
}
