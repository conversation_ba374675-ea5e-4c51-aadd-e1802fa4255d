import { <PERSON><PERSON><PERSON><PERSON>, Render } from "@meitu/whee-infinite-canvas";

export type ElementItem = ({
  type: ElementName.TEXT,
  content: string;
  color: string;
  fontSize: number;
  fontFamily: string;
} | {
  type: ElementName.IMAGE,
  src: string;
}) & {
  topOffset?: number,
}


export type StepItem = (Array<ElementItem>) | {
  elements: Array<ElementItem>,
  // 如果不设置 取steps.gap
  offsetLeft?: number;
}


export type GuideDescription = {
  title: {
    icon: {
      src: string;
    }
    
    text: {
      content: string;
      color: string;
      fontSize: number;
      fontFamily: string;
    }

    // 图标和文字的间隔
    gap: number;
  }

  steps: {
    // 步骤与标题的间隔
    topOffset?: number;
    // 步骤之间的间隔
    gap: number;
    list: Array<StepItem>,
  }
}
