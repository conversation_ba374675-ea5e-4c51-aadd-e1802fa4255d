import { GuideDescription } from "./types";
import assets from "./assets-index.json";
import { fillGuideElements } from "./fillGuideElements";
import { ElementName, Render } from "@meitu/whee-infinite-canvas";
import { tipsColor, titleColor } from "./constants";
import { useI18n } from "@/locales/client";

type FillRemoverGuideOptions = {
  render: Render;
  t: ReturnType<typeof useI18n>;
  defaultFont: string;
};
export async function fillRemoverGuide({
  render,
  t,
  defaultFont,
}: FillRemoverGuideOptions) {
  const bgRemovalGuideDesc: GuideDescription = {
    title: {
      icon: {
        src: assets["remover"]["title-icon"],
      },
      text: {
        content: t("guide.remover.title"),
        fontSize: 24,
        fontFamily: defaultFont,
        color: titleColor,
      },
      gap: 12,
    },

    steps: {
      list: [
        [
          {
            type: ElementName.TEXT,
            content: t("guide.remover.step1"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["common"]["image-icon"],
            topOffset: 16,
          },
        ],
        [
          {
            type: ElementName.TEXT,
            content: t("guide.remover.step2"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["remover"]["remover-icon"],
            topOffset: 16,
          },
          {
            type: ElementName.IMAGE,
            src: assets["remover"]["painter-panel"],
            topOffset: 16,
          },
          {
            type: ElementName.IMAGE,
            src: assets["remover"]["paint-preview"],
            topOffset: 16,
          },
        ],
        {
          elements: [
            {
              type: ElementName.TEXT,
              content: t("guide.remover.step3"),
              fontSize: 14,
              fontFamily: defaultFont,
              color: tipsColor,
              topOffset: 8,
            },
            {
              type: ElementName.IMAGE,
              src: assets["remover"]["result"],
              topOffset: 16,
            },
          ],
          offsetLeft: 48,
        },
      ],
      gap: 48,
      topOffset: 40,
    },
  };

  return await fillGuideElements(bgRemovalGuideDesc, { render });
}
