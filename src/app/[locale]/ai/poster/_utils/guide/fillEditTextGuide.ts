import { GuideDescription } from "./types";
import assets from "./assets-index.json";
import { fillGuideElements } from "./fillGuideElements";
import { ElementName, Render } from "@meitu/whee-infinite-canvas";
import { tipsColor, titleColor } from "./constants";
import { useI18n } from "@/locales/client";

type FillEditTextGuide = {
  render: Render;
  t: ReturnType<typeof useI18n>;
  defaultFont: string;
};
export async function fillEditTextGuide({
  render,
  t,
  defaultFont,
}: FillEditTextGuide) {
  const bgRemovalGuideDesc: GuideDescription = {
    title: {
      icon: {
        src: assets["edit-text"]["title-icon"],
      },
      text: {
        content: t("guide.edit-text.title"),
        fontSize: 24,
        fontFamily: defaultFont,
        color: titleColor,
      },
      gap: 12,
    },

    steps: {
      list: [
        [
          {
            type: ElementName.TEXT,
            content: t("guide.edit-text.step1"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["common"]["image-icon"],
            topOffset: 16,
          },
        ],
        [
          {
            type: ElementName.TEXT,
            content: t("guide.edit-text.step2"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["edit-text"]["edit-text-icon"],
            topOffset: 16,
          },
          {
            type: ElementName.IMAGE,
            src: assets["edit-text"]["preview"],
            topOffset: 16,
          },
        ],
        [
          {
            type: ElementName.TEXT,
            content: t("guide.edit-text.step3"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["edit-text"]["input"],
            topOffset: 16,
          },
        ],
        [
          {
            type: ElementName.TEXT,
            content: t("guide.edit-text.step4"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["edit-text"]["result"],
            topOffset: 16,
          },
        ],
      ],
      gap: 48,
      topOffset: 40,
    },
  };

  return await fillGuideElements(bgRemovalGuideDesc, { render });
}
