import {
  LayerType,
  PosterRatio,
  TemplateConfigType,
  TemplateItemType,
} from "@/api/types/poster";
import { omit } from "lodash";
import { dimensionList, fixedAspectDimensions } from "@/constants/ratio";
import {
  createImage,
  createText,
  ElementName,
  ElementOptions,
  FabricText,
  FrameElementOptions,
  getBoundToViewPortCenterByArrangementToPosition,
  loadFont,
  Point,
  Render,
  resetId,
  StaticCanvas,
} from "@meitu/whee-infinite-canvas";
import {
  FrameElementParams,
  ImageElementParams,
  TextElementParams,
} from "@meitu/whee-infinite-canvas";

type Vector2d = {
  x: number;
  y: number;
};

function createMeasureTextAPI() {
  const canvas = new StaticCanvas();
  const measureFontFamily = "measure_api__";
  return async ({
    fontFamily,
    fontWeight,
    fontSize,
    text,
    fontUrl,
  }: TextElementParams) => {
    try {
      await loadFont([{ fontFamily: measureFontFamily, fontUrl }]);
    } catch (e) {
      if (process.env.NODE_ENV) {
        console.error(
          `字体加载失败: family[${fontFamily}], url[${fontUrl}]`,
          e
        );
      }
    }

    const textNode = new FabricText(text, {
      fontFamily: measureFontFamily,
      fontWeight,
      fontSize,
    });
    canvas.add(textNode);

    const size = {
      width: textNode.width,
      height: textNode.height,
    };

    canvas.remove(textNode);
    return size;
  };
}

const measureText =
  typeof window !== "undefined"
    ? createMeasureTextAPI()
    : () => {
        throw new Error("客户端API，不可在服务端调用");
      };

type Dimension = [number, number];
function toOriginCenter(point: Vector2d, dimension: Dimension) {
  return {
    x: point.x - dimension[0] / 2,
    y: point.y - dimension[1] / 2,
  };
}

function getCenter(
  leftTop: Vector2d,
  size: Dimension,
  rotation: number
): Vector2d {
  const rad = (rotation / 180) * Math.PI;
  const c = Math.cos(rad);
  const s = Math.sin(rad);

  const x = size[0];
  const y = size[1];

  const x1 = c * x - s * y;
  const y1 = s * x + c * y;

  return {
    x: leftTop.x + x1 / 2,
    y: leftTop.y + y1 / 2,
  };
}

const dimensionMap = new Map<PosterRatio, [number, number]>(
  fixedAspectDimensions.map((d) => {
    return [d.value, Array.isArray(d.size) ? d.size : [1, 1]];
  })
);

function transformImageStruct(
  child: TemplateConfigType,
  dimension: Dimension
): ImageElementParams {
  const left = child.marginLeft ?? 0;
  const top = child.marginTop ?? 0;
  const rotation = child.rotation ?? 0;
  const width = child.width ?? 128;
  const height = child.height ?? 128;
  const center = getCenter({ x: left, y: top }, [width, height], rotation);
  const originCenter = toOriginCenter(center, dimension);

  return {
    src: child.pic ?? "",
    width: child.width ?? 128,
    height: child.height ?? 128,
    left: originCenter.x ?? 0,
    top: originCenter.y ?? 0,
    angle: rotation ?? 0,
    _name_: ElementName.IMAGE,
  };
}

async function transformTextStruct(
  child: TemplateConfigType,
  dimension: Dimension
): Promise<TextElementParams> {
  const fontFamily = child.fontName ?? "";
  const fontSize = child.fontSize ?? 12;
  const fontWeight = "";
  const fontUrl = child.subsetWofFontFile || child.subsetFontFile || "";
  const text = child.text ?? "";
  const left = child.marginLeft ?? 0;
  const top = child.marginTop ?? 0;
  const rotation = child.rotation ?? 0;

  const { width, height } = await measureText({
    fontFamily,
    fontWeight,
    fontSize,
    fontUrl,
    text,
  });

  const center = getCenter({ x: left, y: top }, [width, height], rotation);
  const originCenter = toOriginCenter(center, dimension);
  return {
    fontFamily: child.fontName ?? "",
    fontSize: child.fontSize ?? 12,
    fontWeight: "",
    fontUrl: child.subsetWofFontFile || child.subsetFontFile || "",
    text: child.text ?? "",
    left: originCenter.x ?? 0,
    top: originCenter.y ?? 0,
    angle: rotation ?? 0,
    _name_: ElementName.TEXT,
    fill: "#000",
  };
}

async function transformStruct(
  template: TemplateItemType
): Promise<FrameElementParams | undefined> {
  const dimension = dimensionMap.get(template.ratio as PosterRatio);
  if (!dimension) {
    return;
  }

  const [width, height] = dimension;

  return {
    width,
    height,
    backgroundColor: "#fff",
    children: await Promise.all(
      template.config.map((child) => {
        switch (child.type) {
          case LayerType.Text:
            return transformTextStruct(child, dimension);
          case LayerType.Image:
            return transformImageStruct(child, dimension);
        }
      })
    ),
  };
}

export async function insertTemplate(
  render: Render,
  template: TemplateItemType,
  guidePlaceholder = false,
) {

  const isNewTemplate = template.newConfig?.v2
  if (isNewTemplate) {
    return await insertNewTemplate(render, template, guidePlaceholder)
  }

  const params = await transformStruct(template);
  if (!params) {
    return;
  }

  // 根据模版比例获取封面图宽高
  // const temp = dimensionList.find((item) => item.value === template.ratio);
  // 插入左侧预览图
  // const image = await createImage("", {
  //   src: toAtlasImageView2URL(template.pic, {
  //     mode: 2,
  //     width: Number(temp?.size?.[0]),
  //     height: Number(temp?.size?.[1]),
  //   }),
  //   _custom_data_history_: {
  //     // 标记这张图片是模版预览图或者由模版预览图生成
  //     isFromTemplatePreview: true,
  //   }
  // });
  // render.addToViewPortCenterByArrangement(image);

  // 插入模版名称
  // const text = await createText("", {
  //   text: `Template -${template.name} renderings`,
  //   fontSize: 50,
  //   fill: "#fff",
  // });
  // text.setXY(
  //   new Point(image.left, image.top + image.height / 2 + FrameTransformX)
  // );
  // render.add(text);
  // console.log("params", params);

  // 插入画板
  const frame = await render.addFrame({
    ...params,
    labelText: template.name,
  });

  if (guidePlaceholder && frame) {
    frame.set({
      left: frame.left + frame.getScaledWidth(),
    });
  }

  // 添加自定义数据
  frame?.set({
    _custom_data_history_: {
      ...frame?._custom_data_history_,
      params: {
        ...template,
        picRatio: template.ratio,
      },
      template: {
        id: template.id,
        categoryId: template.categoryId,
        name: template.name,
      }
    },
  });

  return { frame };
}

export async function  insertNewTemplate(render: Render, template: TemplateItemType, guidePlaceholder = false) {
  let params = template.newConfig.data;
  params = resetId([params as FrameElementOptions], "")[0] as unknown as FrameElementOptions;
  // 根据模版比例获取封面图宽高
  // const temp = dimensionList.find((item) => item.value === template.ratio);
  // 插入左侧预览图
  // const image = await createImage("", {
  //   src: toAtlasImageView2URL(template.pic, {
  //     mode: 2,
  //     width: !temp ? params.width * (params.scaleX ?? 1) : Number(temp?.size?.[0]),
  //     height: !temp ? params.height * (params.scaleY ?? 1) : Number(temp?.size?.[1]),
  //   }),
  //   _custom_data_history_: {
  //     // 标记这张图片是模版预览图或者由模版预览图生成
  //     isFromTemplatePreview: true,
  //   }
  // });
  // render.addToViewPortCenterByArrangement(image);

  // 插入模版名称
  // const text = await createText("", {
  //   text: `Template -${template.name} renderings`,
  //   fontSize: 50,
  //   fill: "#fff",
  // });
  // text.setXY(
  //   new Point(image.left, image.top + image.height / 2 + FrameTransformX)
  // );
  // render.add(text);

  const { width, height } = template.newConfig.data;
  const { left, top } = getBoundToViewPortCenterByArrangementToPosition.call(
    render,
    guidePlaceholder ? width * 2 : width,
    // width,
    height
  );
  const elements = await render.setSceneData([{
    ...params,
    labelText: template.name,
    // left: image.left + image.getScaledWidth() + FrameTransformX,
    left: guidePlaceholder ? left + width / 2 : left,
    top,
  } as ElementOptions])
  const targetFrame = elements[0]
  const newTemplate = omit(template, ["newConfig", "config"])

  // if (guidePlaceholder && targetFrame) {
  //   targetFrame.set({
  //     left: targetFrame.left + targetFrame.getScaledWidth(),
  //   });
  // }

  targetFrame?.set({
    _custom_data_history_: {
      ...targetFrame?._custom_data_history_,
      params: {
        ...newTemplate,
        picRatio: template.ratio,
        newConfig: undefined,
        config: undefined
      },
      template: {
        id: template.id,
        categoryId: template.categoryId,
        name: template.name,
      }
    },
  });
  return { frame: targetFrame }
}