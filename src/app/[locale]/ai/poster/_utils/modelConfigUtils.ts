import { ControlNetUnitsType } from "@/api/types/aiPoster/task";
import {
  EditorConfigModelResponse,
  ModelCardProps,
  BaseModelInfo,
} from "@/api/types/editorConfig";
import { ControlNetParams } from "@/types";
import { toAtlasImageView2URL } from "@meitu/util";
import _ from "lodash";

// 获取所有基础模型的平铺列表
export const getAllModelByConfig = (
  modelConfig: EditorConfigModelResponse[]
): Required<BaseModelInfo>[] => {
  const allModels = modelConfig.flatMap((config) => config.list ?? []);
  const uniqueModels = allModels.filter(
    (model, index) => allModels.findIndex((m) => m.id === model.id) === index
  );

  return uniqueModels.map((model) => {
    const image = Array.isArray(model.images) ? model.images[0] : model.images;
    return {
      id: model.id,
      src:
        image &&
        toAtlasImageView2URL(image, {
          mode: 2,
          width: 116,
          height: 116,
        }),
      title: model.name,
      desc: model.desc,
      tag: model.typeName,
      cornerLabelUrl: model.tag?.url ?? "",
      baseModelDefaultParams: model.defaultParams,
      mvType: model.mvType,
    };
  });
};

// 获取模型的分类id
export const getCategoryIds = (
  configs: EditorConfigModelResponse[],
  itemId: number
): string => {
  const categoryIds = configs
    .filter((config) => config.list?.some((item) => item.id === itemId))
    .map((config) => config.categoryId);

  return categoryIds.join(",");
};

// 获取模型索引
export const getModelById = <T extends Record<string, any>>(
  key: keyof T,
  models?: T[]
) => {
  return (id?: number) => models?.findIndex((model) => id === model[key]) ?? -1;
};

type EvolveTransforms<T extends Record<string, any>> = {
  [P in keyof T]?: (value: T[P]) => T[P];
};
function evolve<T extends Record<string, any>>(
  transforms: EvolveTransforms<T>
) {
  return (value: T) => {
    const nextValue = _.mapValues(transforms, (transform, key) =>
      transform?.(value[key])
    );

    return Object.assign({}, value, nextValue) as T;
  };
}
/**
 * 前端controlNet -> 服务端controlnetUnits
 * @param controlNets
 * @returns
 */
export function formatControlNet(
  controlNets: ControlNetParams[],
  transforms?: EvolveTransforms<ControlNetParams>
) {
  const transformer = transforms
    ? evolve(transforms)
    : _.identity<ControlNetParams>;

  return controlNets.map((controlNet) => {
    const { interventionTiming, zoomMode, imageProcessParams, ...rest } =
      transformer(controlNet);

    return {
      enabled: !!imageProcessParams?.image,
      guidanceStart: interventionTiming?.[0],
      guidanceEnd: interventionTiming?.[1],
      resizeMode: zoomMode,
      ...rest,
      inputImage: imageProcessParams?.image,
      model: imageProcessParams?.model,
      module: imageProcessParams?.module,
      modelId: imageProcessParams?.modelId,
    };
  });
}

/**
 * 服务端controlnetUnits -> 前端controlNet
 * @param controlnetUnits
 * @returns
 */
export function getControlNet(
  controlnetUnits: ControlNetUnitsType[],
  defaultControlNet: ControlNetParams
) {
  console.log("controlnetUnits", controlnetUnits);

  const controlNet = controlnetUnits?.map(
    ({
      enabled,
      guidanceStart,
      guidanceEnd,
      resizeMode,
      inputImage,
      module,
      model,
      modelId,
      ...rest
    }) => ({
      enable: enabled,
      interventionTiming: [guidanceStart, guidanceEnd] as [number, number],
      zoomMode: resizeMode,
      inputImage,
      model,
      module,
      ...rest,
      imageProcessParams: {
        image: inputImage,
        model,
        module,
        modelId: modelId,
      },
    })
  );
  if (!defaultControlNet) {
    return controlNet;
  }

  /** 此逻辑是因为后端把 enable = false 的过滤掉了 这样会导致用户未开启的配置被删除且顺序会错乱  默认值会被清空 重新填充默认值*/
  while (controlNet?.length < 3) {
    controlNet.push(defaultControlNet);
  }

  // 如果服务端没有下发inputImage 就表示不能回填
  return controlNet?.map((unit) => {
    if (!unit.inputImage) {
      return defaultControlNet;
    }

    return unit;
  });
}
