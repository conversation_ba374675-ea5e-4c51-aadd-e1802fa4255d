import {
  ElementOptions,
  FabricObject,
  getElementOptions,
  Group,
  HistoryPlugins,
  Render,
} from "@meitu/whee-infinite-canvas";
import { TasksPollingManager } from "../TasksPollingManager";
import { RootStore } from "../../_store";

type DispatcherHelperParams = {
  rootStore?: RootStore;
  msgId: string;
};

type DispatcherHelper = {
  // shape: FabricObject;
  tasksPollingManager: TasksPollingManager;
  projectId: number;
  render: Render;
  historyPlugins: HistoryPlugins;
  findShapeNode: () => null | {
    node: FabricObject;
    options: ElementOptions;
    customData: Record<string, any>;
  };
};

export function dispatcherHelper({
  rootStore,
  msgId,
}: DispatcherHelperParams): DispatcherHelper | null {
  if (!rootStore) {
    return null;
  }

  const {
    renderStore,
    pollingManagers: { tasksPollingManager },
    projectsStore,
  } = rootStore;
  const render = renderStore.render;
  const historyPlugins = renderStore.historyPlugins;
  const projectId = projectsStore.activeProjectId;

  if (!render || !historyPlugins || !projectId) {
    return null;
  }

  const findShape = () => {
    const shape = render.Finder.findByCondition(obj => {
      const options = getElementOptions.call(render, obj);
      return options._custom_data_history_?.msgId === msgId;
    }, render._FC);

    if (!shape) {
      return null;
    }

    const shapeOptions = getElementOptions.call(render, shape);
    const customData = shapeOptions._custom_data_history_ ?? {};

    return {
      node: shape,
      options: shapeOptions,
      customData,
    };
  };

  return {
    render,
    historyPlugins,
    projectId,
    tasksPollingManager,
    findShapeNode: findShape,
  };
}
