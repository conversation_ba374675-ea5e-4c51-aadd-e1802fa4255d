import { AIPoster, AlphaStatus } from '@/api/types/aiPoster/task';

export function needsProcessImage(task: AIPoster.Task) {
  switch (task.taskCategory) {
    case AIPoster.TaskCategory.Upscaler:
      return upscalerNeedsReplaceAlpha(task);
  }

  return false;
}

export function upscalerNeedsReplaceAlpha(task: AIPoster.Task) {
  const firstResult = task.resultImages.find(
    (item) => item.imageStatus === AIPoster.ImageStatus.Success
  );
  
  let src = firstResult?.urlSign;
  const urlShort = firstResult?.urlShort;

  if (!firstResult || !src || !urlShort) {
    return false;
  }

  // 需要替换alpha通道
  return firstResult.alphaStatus === AlphaStatus.HasAlpha;
}
