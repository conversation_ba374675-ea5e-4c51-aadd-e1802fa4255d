import { AIPoster } from "@/api/types/aiPoster/task";
import { Group } from "@meitu/whee-infinite-canvas";
import { getImageEditStatus } from "../../imageEdit";
import { HandleTaskFinish } from "./types";
import { dispatcherHelper } from "../helper";
import { trackEvent } from "@meitu/subscribe-intl";
import { Track } from "../../../_constant/track";

export function handleTextEditFinish({
  task,
  abortController,
  rootStore,
}: HandleTaskFinish) {
  const helper = dispatcherHelper({ rootStore, msgId: task.id });
  if (!helper) {
    return;
  }

  const { findShapeNode, render } = helper;

  const shape = findShapeNode();
  if (!shape) {
    return;
  }

  const image = shape.node;

  const firstResult = task.resultImages.find(
    (item) => item.imageStatus === AIPoster.ImageStatus.Success
  );
  const src = firstResult?.urlSign;
  const urlShort = firstResult?.urlShort;
  const msgId = task.id;

  // 如果没有找到图片
  if (!firstResult || !src || !urlShort) {
    // 全部失败
    image?.set({
      _custom_data_history_: {
        ...image._custom_data_history_,
        isHaveCompletedTaskId: msgId,
      },
    });
    return Promise.reject("无成功图片");
  }

  trackEvent("ai_create_image_success", {
    function: Track.FunctionEnum.ModifyText,
    task_id: msgId,
    is_leave: rootStore?.editorStatusStore.hasActiveTask(msgId) ? 0 : 1,
  });

  return render.Actions.replaceImage(
    image as Group,
    {
      // 1. 替换当前图形的图片
      src,
      _custom_data_history_: {
        ...image._custom_data_history_,
        ...getImageEditStatus(AIPoster.TaskCategory.TextEdit),
        msgId,
        urlShort: urlShort,
        isHaveCompletedTaskId: msgId,
      },
      width: image.getScaledWidth(),
      height: image.getScaledHeight(),
      scaleX: 1,
      scaleY: 1,
      _parent_id_: shape.options._parent_id_,
    },
    "center",
    abortController?.signal
  );
}
