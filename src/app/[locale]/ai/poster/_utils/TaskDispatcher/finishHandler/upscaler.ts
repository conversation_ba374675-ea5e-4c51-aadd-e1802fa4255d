import { AIPoster, AlphaStatus } from "@/api/types/aiPoster/task";
import { Group } from "@meitu/whee-infinite-canvas";
import { _replace<PERSON>lpha, getImageEditStatus } from "../../imageEdit";
import { replaceUpscaleImage } from "@/api/image";
import { dispatcherHelper } from "../helper";
import { HandleTaskFinish } from "./types";
import { trackEvent } from "@meitu/subscribe-intl";
import { Track } from "../../../_constant/track";

export async function handleUpscalerFinish({
  task,
  abortController,
  rootStore,
}: HandleTaskFinish) {
  const helper = dispatcherHelper({ rootStore, msgId: task.id });
  if (!helper) {
    return;
  }

  const { findShapeNode, render } = helper;
  let image = findShapeNode()?.node;

  const firstResult = task.resultImages.find(
    (item) => item.imageStatus === AIPoster.ImageStatus.Success
  );
  // 如果没有找到图片
  let src = firstResult?.urlSign;
  const msgId = task.id;

  if (!firstResult || !src) {
    // 全部失败
    image?.set({
      _custom_data_history_: {
        ...image._custom_data_history_,
      },
    });
    return Promise.reject("没找到图片");
  }

  trackEvent("ai_create_image_success", {
    function: Track.FunctionEnum.ImageEnhancer,
    task_id: msgId,
    is_leave: rootStore?.editorStatusStore.hasActiveTask(msgId) ? 0 : 1,
  });

  // 需要替换alpha通道
  if (firstResult.alphaStatus === AlphaStatus.HasAlpha) {
    const resImg = firstResult.urlSign;
    const originImg = task.params.initImages?.[0];

    const res = await _replaceAlpha(resImg, originImg || "");

    if (!res) return;
    const resultRep = await replaceUpscaleImage({
      id: task.id,
      resultImageUrl: res.url,
    });

    // 替换完成之后  刷新记录列表
    rootStore?.generateRecordStore.fetchHistory({ enterStatus: "updating" });

    if (resultRep.result) {
      src = resultRep.resultImage;
    }
  }

  image = findShapeNode()?.node;
  if (!image) {
    return Promise.reject("图形不在画布中");
  }

  return render.Actions.replaceImage(
    image as Group,
    {
      // 1. 替换当前图形的图片
      src,
      width: firstResult?.width,
      height: firstResult?.height,
      scaleX: 1,
      scaleY: 1,
      _custom_data_history_: {
        ...image._custom_data_history_,
        ...getImageEditStatus(AIPoster.TaskCategory.Upscaler),
        msgId,
        urlShort: src,
        isHaveCompletedTaskId: null,
      },
      _parent_id_: findShapeNode()?.options._parent_id_,
    },
    "lt",
    abortController?.signal
  );
}
