import { FabricObject, getElementOptions } from "@meitu/whee-infinite-canvas";
import { pollingQuery } from "./pollingQuery";
import { handleFinish } from "./finishHandler";
import { saveElements } from "../saveProjectHistory";
import { message } from "antd";
import { AIPoster } from "@/api/types/aiPoster/task";
import { dispatcherHelper } from "./helper";
import { RootStore } from "../../_store";
import toast from "@/components/Toast";
import { useI18n } from "@/locales/client";

type DispatchParams = {
  msgId: string;
  shape?: FabricObject;
  abortController?: AbortController;
  rootStore?: RootStore;
  expandCustomData?: Record<string, any>;
  t: ReturnType<typeof useI18n>;
};
export function dispatch({
  msgId,
  abortController,
  shape,
  rootStore,
  expandCustomData,
  t,
}: DispatchParams) {
  const helper = dispatcherHelper({ rootStore, msgId });
  if (!helper) {
    return Promise.resolve();
  }

  const {
    findShapeNode,
    render,
    projectId,
    historyPlugins,
    tasksPollingManager,
  } = helper;

  const dispatchShape = shape || findShapeNode()?.node;
  const saveLoadingStatus = () => {
    if (!dispatchShape) {
      return Promise.resolve();
    }

    if (
      dispatchShape._custom_data_history_.msgId === msgId &&
      !expandCustomData
    ) {
      return Promise.resolve();
    }

    dispatchShape.set({
      _custom_data_history_: {
        ...dispatchShape._custom_data_history_,
        ...expandCustomData,
        msgId,
      },
    });

    return saveElements({
      targets: [dispatchShape],
      render,
      projectId,
    });
  };

  // 处理前记录数据
  const beforeData =
    dispatchShape &&
    historyPlugins.baseAction.getElementData({
      target: dispatchShape,
    });

  const handleTaskEndSaveSubmit = (taskIsSuccess: boolean) => {
    const shape = findShapeNode();
    if (!shape || !beforeData) {
      return;
    }

    const shapeOptions = getElementOptions.call(render, shape.node);
    const submitShapeOption = shapeOptions;
    //任务失败， 失败的任务 不同步到服务端记录,记录当前的自定义数据
    if (!taskIsSuccess) {
      submitShapeOption._custom_data_history_ =
        beforeData[0]._custom_data_history_;
    }
    return saveElements({
      targets: [],
      operations: [submitShapeOption],
      render,
      projectId,
    });
  };

  return saveLoadingStatus()
    .then(() => {
      // 任务创建成功 开始轮训
      tasksPollingManager.appendId([msgId], projectId);

      return pollingQuery({
        tasksPollingManager,
        abortController,
        msgId,
      });
    })
    .then((finish) => {
      // console.log("finish", finish);

      if (!finish || abortController?.signal.aborted) {
        return;
      }
      const isSuccess = finish.loadingStatus === AIPoster.LoadingStatus.Success;
      if (!isSuccess) {
        toast.error(t("Failed to generate. Please try again."));
      }

      //本地历史剔除loading态
      if (beforeData) {
        beforeData[0]._loading_ = false;
      }

      return Promise.resolve(
        handleFinish({
          type: finish.taskCategory,
          task: finish,
          abortController,
          rootStore,
          t,
        })
      ).then(() => {
        // console.log(
        //   "abortController?.signal.aborted",
        //   abortController?.signal.aborted
        // );

        if (abortController?.signal.aborted) {
          return;
        }

        const shape = findShapeNode();

        // console.log("shape", shape);
        // console.log("beforeData", beforeData);

        if (!shape || !beforeData) {
          return;
        }

        render.Actions.setLoaded(shape.node._id_);
        // 记录当前图片已经扣过图了 提交历史
        const afterData =
          historyPlugins.baseAction.getElementData({
            target: shape.node,
          }) ?? [];
        const operation = historyPlugins.baseAction.getModifiedOperation({
          beforeData,
          afterData,
        });

        if (operation) {
          historyPlugins.submit(operation);
        }
        handleTaskEndSaveSubmit(isSuccess);
      });
    })
    .catch((e) => {
      if (process.env.NODE_ENV === "development") {
        console.log("轮训任务异常", e);
      }
      const shape = findShapeNode();
      if (!shape || !beforeData) {
        return;
      }

      render.Actions.setLoaded(shape.node._id_);
      handleTaskEndSaveSubmit(false);
    });
}
