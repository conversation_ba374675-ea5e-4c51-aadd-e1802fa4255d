type FrameGuideRecordItem = {
  template?: {shown: boolean};
  frame?: {shown: boolean};
}

type RecordsBook = {
  [uid in number]: FrameGuideRecordItem;
}

const bookKey = 'poster-editor/frame-guide';

function getBook(): RecordsBook {
  const bookJSONStr = localStorage.getItem(bookKey);
  if (!bookJSONStr) {
    return {};
  }
  
  try {
    return JSON.parse(bookJSONStr);
  } catch(e: any) {
    return {}
  }
}

function getRecord(uid: number, feature: keyof FrameGuideRecordItem) {
  const book = getBook();
  const userRecord = book[uid] ?? {};
  book[uid] = userRecord;

  const featureRecord = userRecord[feature] ?? {shown: false};
  userRecord[feature] = featureRecord;
  return {book, featureRecord};
}


export function recordGuideShown(uid: number, feature: keyof FrameGuideRecordItem) {
  const {book, featureRecord} = getRecord(uid, feature);
  featureRecord.shown = true;
  localStorage.setItem(bookKey, JSON.stringify(book));
}

export function needShowGuide(uid: number, feature: keyof FrameGuideRecordItem) {
  const {featureRecord} = getRecord(uid, feature);
  return !featureRecord.shown;
}