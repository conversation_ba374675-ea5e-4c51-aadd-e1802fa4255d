import { createImage, createText, FabricObject, FixedLayout, Group, LayoutManager, Render } from "@meitu/whee-infinite-canvas";
import assets from './assets-index.json';
type CommonOptions = {
  marginTop?: number;
  marginRight?: number;
  marginLeft?: number;

  // 用来调整图片本身的大小。如果是4倍图，想要展示成与原来一般的大小，scale为0.25
  scale?: number;
}

type TextOptions = CommonOptions & {
  text: string;
  fontSize: number;
  fontFamily: string;
}

export type InsertFrameGuideOptions = {
  frame: {
    left: number;
    top: number;
    width: number;
    height: number;
  }

  guide: {
    width: number;
    marginRight: number;
    title: TextOptions;
    steps: Array<CommonOptions & {
      content: TextOptions,
    }>;
    tips: TextOptions;
    previewImage: CommonOptions;
  }
}

export async function insertFrameGuide(render: Render, options: InsertFrameGuideOptions) {
  const { guide, frame } = options;
  const frameLeft = frame.left - frame.width / 2;
  const frameTop = frame.top - frame.height / 2;
  const guideRight = frameLeft - guide.marginRight;
  const guideLeft = guideRight - guide.width;

  let offsetTop = frameTop;
  const elements = [] as Array<FabricObject>;

  const group = new Group();
  const add = (shape: FabricObject) => {
    group.add(shape);
    elements.push(shape);
  }

  // 绘制title
  {
    const title = createText('', {
      text: guide.title.text,
      fill: '#ffffff',
      fontSize: guide.title.fontSize,
      fontFamily: guide.title.fontFamily,
    });

    title.set({
      left: guideLeft + title.getScaledWidth() / 2,
      top: offsetTop + title.getScaledHeight() / 2,
    });

    add(title);
    offsetTop += title.getScaledHeight();
  }

  // 绘制steps
  {
    for(let i = 0; i < guide.steps.length; ++i) {
      const step = guide.steps[i];
      let offsetLeft = guideLeft;
      offsetTop += (step.marginTop ?? 0);

      const icon = await createImage('', {
        src: assets.steps[i],
        scaleX: 0.25,
        scaleY: 0.25,
      });

      icon.set({
        left: offsetLeft + icon.getScaledWidth() / 2,
        top:  offsetTop + icon.getScaledHeight() / 2,
      });
      add(icon);

      offsetLeft = offsetLeft + icon.getScaledWidth() + (step.marginLeft ?? 0);

      const stepContent = createText('', {
        text: step.content.text,
        fill: '#A3AEBF',
        fontSize: step.content.fontSize,
        fontFamily: step.content.fontFamily,
      });

      stepContent.set({
        left: offsetLeft + stepContent.getScaledWidth() / 2 ,
        top: offsetTop + stepContent.getScaledHeight() / 2 + (step.content.marginTop ?? 0),
      });
      add(stepContent);
      offsetTop += Math.max(icon.getScaledHeight(), stepContent.getScaledHeight() + (step.content.marginTop ?? 0));
      // console.log(offsetLeft, offsetTop)
    }
  }

  // 绘制content
  {
    const tips = createText('', {
      text: guide.tips.text,
      fill: '#A3AEBF',
      fontSize: guide.tips.fontSize,
      fontFamily: guide.tips.fontFamily,
    });

    offsetTop += guide.tips.marginTop ?? 0,
    tips.set({
      left: guideLeft + tips.getScaledWidth() / 2,
      top: offsetTop + tips.getScaledHeight() / 2,
    })
    offsetTop += tips.getScaledHeight();
    add(tips);
  }

  {
    const preview = await createImage('', {
      src: assets.preview,
      scaleX: guide.previewImage.scale ?? 1,
      scaleY: guide.previewImage.scale ?? 1,
    });

    offsetTop += guide.previewImage.marginTop ?? 0;
    preview.set({
      left: guideRight - guide.marginRight - preview.getScaledWidth() / 2,
      top: offsetTop + preview.getScaledHeight() / 2,
    });
    offsetTop += preview.getScaledHeight();
    add(preview);
  }

  // 用来调整缩放后的偏移
  const before = {
    x: group.getScaledWidth() / 2,
    y: group.getScaledHeight() / 2,
  }

  group.scale(Math.max(1, Math.min(frame.width / group.getScaledWidth(), frame.height / group.getScaledHeight())));

  const after = {
    x: group.getScaledWidth() / 2,
    y: group.getScaledHeight() / 2,
  }

  group.set({
    left: group.left - (after.x - before.x),
    top:  group.top  + (after.y - before.y),
  });

  group.removeAll();
  render.add(...elements);
  return elements;
}
