import {
  ContainerElementOptions,
  DeltaType,
  ElementOptions,
  FabricObject,
  getElementOptions,
  HistoryChangeType,
  Operation,
  Render
} from '@meitu/whee-infinite-canvas';
import { LayerConfig } from '@/api/types/aiPoster/element';
import { deleteElement, saveElement } from '@/api/aiPoster/element';
import { handleProjectDeletedError } from './projectDeletedErrorHandler';
import _ from 'lodash';

//构建保存项目历史参数
export const buildSaveElementsParams = (targetsOptions: ElementOptions[]) => {
  const layers: LayerConfig[] = [];
  targetsOptions.forEach((option) => {
    const { children, ...editorParams } = option as ContainerElementOptions;
    layers.push({
      layerId: option._id_,
      editorParams: JSON.stringify(_.omit(editorParams, ['_mask_options_']))
    });
    if (children) {
      flattenOptions(children).forEach((child) => {
        layers.push({
          layerId: child._id_,
          editorParams: JSON.stringify(_.omit(child, ['_mask_options_']))
        });
      });
    }
  });
  return layers;
};

//将option的children树结构拉平
export const flattenOptions = (objects: ElementOptions[]) => {
  const result: ElementOptions[] = [];
  const stack = [...objects];

  while (stack.length > 0) {
    const current = stack.shift();
    if (!current) continue;

    result.push(current);

    if ('children' in current && Array.isArray(current.children)) {
      stack.unshift(...current.children);
    }
  }
  return result;
};

//处理画布历史redo undo 提交项目历史参数
export const handleHistoryOperationsParams = ({
  operations,
  historyChangeType
}: {
  operations: Operation[];
  historyChangeType: HistoryChangeType;
}) => {
  const deleteOptions: ElementOptions[] = [];
  const saveOptions: ElementOptions[] = [];
  operations.forEach((operation) => {
    const objects =
      operation.options.preData?.objects ??
      operation.options.afterData?.objects ??
      [];
    if (historyChangeType === HistoryChangeType.UNDO) {
      if (operation.options.type === DeltaType.MODIFY) {
        saveOptions.push(...(operation.options.preData?.objects ?? []));
      } else if (operation.options.type === DeltaType.INSERT) {
        deleteOptions.push(...objects);
      } else {
        saveOptions.push(...objects);
      }
    } else if (historyChangeType === HistoryChangeType.REDO) {
      if (operation.options.type === DeltaType.MODIFY) {
        saveOptions.push(...(operation.options.afterData?.objects ?? []));
      } else if (operation.options.type === DeltaType.DELETE) {
        deleteOptions.push(...objects);
      } else {
        saveOptions.push(...objects);
      }
    }
  });
  return { deleteOptions, saveOptions };
};

type SubmitElementsParams = {
  targets: FabricObject[];
  operations?: ElementOptions[];
  render: Render;
  projectId: number;
};

export async function saveElements({
  targets,
  operations,
  render,
  projectId
}: SubmitElementsParams) {
  if (!render) return;
  //如果有options直接使用options
  let targetsOptions: ElementOptions[] = [];
  if (operations) {
    targetsOptions = operations;
  } else {
    targetsOptions = render.setOptionsZIndex(
      targets.map((target) => {
        return getElementOptions.call(render, target);
      })
    );
  }
  if (!targetsOptions.length) return;
  const layers = buildSaveElementsParams(targetsOptions);
  try {
    await saveElement({
      projectId,
      layers
    });
    // console.log(res, '保存历史-----');
  } catch (err: any) {
    // console.log(err, '保存异常-----');
    handleProjectDeletedError(err);
  }
}

export async function deleteElements({
  targets,
  operations,
  render,
  projectId
}: SubmitElementsParams) {
  if (!render) return;
  //如果有options直接使用options
  let targetsOptions: ElementOptions[] = [];
  if (operations) {
    targetsOptions = operations;
  } else {
    targetsOptions = render.setOptionsZIndex(
      targets.map((target) => {
        return getElementOptions.call(render, target);
      })
    );
  }
  if (!targetsOptions.length) return;
  const layerIds = buildSaveElementsParams(targetsOptions)
    .map((layer) => layer.layerId)
    .join(',');
  try {
    await deleteElement({
      projectId,
      layerIds
    });
  } catch (err) {
    // console.log(err, '删除异常-----');
  }
}
