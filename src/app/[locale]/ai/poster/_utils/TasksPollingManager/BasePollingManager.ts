import EventEmitter from 'events';

export enum QueryStatus {
  Pending = 'pending',
  Resolved = 'resolved'
}

export abstract class BasePollingManager<QueryId, QueryFunctionResponse> {
  private _pollingIds = [] as Array<QueryId>;
  private _projectId: number | null = null;

  private _eventBus = new EventEmitter();
  private static ResolvedEventKey = Symbol('resolved');
  private static PendingEventKey = Symbol('pending');

  constructor(private intervals = 3 * 1000) {}

  protected abstract query(
    ids: Array<QueryId>,
    projectId: number
  ): Promise<Array<QueryFunctionResponse>>;

  protected abstract getQueryStatus(res: QueryFunctionResponse): QueryStatus;

  protected abstract getQueryId(res: QueryFunctionResponse): QueryId;

  public appendId(ids: Array<QueryId>, projectId: number) {
    if (this._projectId !== projectId) {
      this.stop();
      this._projectId = projectId;
      this._pollingIds.length = 0;
    }

    const idSet = new Set([...this._pollingIds, ...ids]);
    this._pollingIds = Array.from(idSet);
    this.flushPollingIds();
  }

  public addTaskResolvedListener(
    handler: (payload: { resolved: QueryFunctionResponse[] }) => any
  ) {
    this._eventBus.addListener(BasePollingManager.ResolvedEventKey, handler);
  }

  public removeTaskResolvedListener(
    handler: (payload: { resolved: QueryFunctionResponse[] }) => any
  ) {
    this._eventBus.removeListener(BasePollingManager.ResolvedEventKey, handler);
  }

  public addTaskPendingListener(
    handler: (payload: { pending: QueryFunctionResponse[] }) => any
  ) {
    this._eventBus.addListener(BasePollingManager.ResolvedEventKey, handler);
  }

  public removeTaskPendingListener(
    handler: (payload: { pending: QueryFunctionResponse[] }) => any
  ) {
    this._eventBus.removeListener(BasePollingManager.ResolvedEventKey, handler);
  }

  private _timeoutKey = null as null | NodeJS.Timeout;
  private flushPollingIds() {
    const ids = this._pollingIds.slice(0);

    if (!ids.length) {
      return;
    }

    const clear = () => {
      if (!this._timeoutKey) {
        return;
      }

      clearTimeout(this._timeoutKey);
      this._timeoutKey = null;
    };

    let delay = 0;
    if (this._timeoutKey) {
      delay = this.intervals;
      clear();
    }

    try {
      this._timeoutKey = setTimeout(() => {
        this.queryTick(ids).finally(() => {
          if (!this._pollingIds.length) {
            clear();
          }
        });
      }, delay);
    } catch (e) {
      // console.log(e);
    }
  }

  private _queryTimestamp = Date.now();
  private async queryTick(ids: Array<QueryId>) {
    if (!this._projectId) {
      this.stop();
      return;
    }

    const privateQueryTimestamp = Date.now();
    this._queryTimestamp = privateQueryTimestamp;
    let results = [] as Array<QueryFunctionResponse>;
    try {
      results = await this.query(ids, this._projectId);
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
        console.error(e);
      }
    }

    if (privateQueryTimestamp !== this._queryTimestamp) {
      return;
    }

    if (!results.length) {
      return this.flushPollingIds();
    }

    const [resolved, pending] = results.reduce(
      ([resolved, pending], result) => {
        const status = this.getQueryStatus(result);
        switch (status) {
          case QueryStatus.Pending: {
            pending.push(result);
            break;
          }

          case QueryStatus.Resolved: {
            resolved.push(result);
            break;
          }
        }
        return [resolved, pending] as [
          Array<QueryFunctionResponse>,
          Array<QueryFunctionResponse>
        ];
      },
      [[], []] as [Array<QueryFunctionResponse>, Array<QueryFunctionResponse>]
    );

    if (resolved.length) {
      this.dispatchResolved(resolved);
    }

    const pendingIdSet = new Set(pending.map((p) => this.getQueryId(p)));
    this._pollingIds = Array.from(pendingIdSet);
    if (pending.length) {
      this.dispatchPending(pending);
      this.flushPollingIds();
    }
  }

  public stop() {
    this._queryTimestamp = Date.now();
    this._pollingIds.length = 0;

    if (this._timeoutKey) {
      clearTimeout(this._timeoutKey);
      this._timeoutKey = null;
    }
  }

  private dispatchResolved(resolved: Array<QueryFunctionResponse>) {
    this._eventBus.emit(BasePollingManager.ResolvedEventKey, { resolved });
  }

  private dispatchPending(pending: Array<QueryFunctionResponse>) {
    this._eventBus.emit(BasePollingManager.PendingEventKey, { pending });
  }
}
