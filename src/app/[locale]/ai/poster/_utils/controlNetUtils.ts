import { EditorConfigModuleListResponse } from "@/api/types/editorConfig";

/**
 * 获取埋点相关参数
 * @param ControlNetParams
 */
export function getControlNetTrackParams(
  controlnetUnits?: any,
  moduleList?: EditorConfigModuleListResponse[]
) {
  if (!controlnetUnits || !controlnetUnits.length || !moduleList) {
    return {};
  }

  const controlNetsMap = new Map(
    moduleList
      .flatMap(({ list }) => list)
      .map((controlnet) => {
        return [controlnet.id, controlnet];
      })
  );

  const controlnetUnitsParams = controlnetUnits.map(
    (unit: { modelId: number; enabled: any; inputImage: any }) => {
      const modelConfig = controlNetsMap.get(unit.modelId);
      const examplePics = modelConfig?.choosablePics;

      // 是否使用了示例图片
      const isExamplePic =
        unit.enabled &&
        containsUrlWithoutQuery(examplePics ?? [], unit.inputImage ?? "");

      return {
        ...unit,
        controlnet_model_name: modelConfig?.name ?? "",
        controlnet_pic: isExamplePic ? 1 : 0,
      };
    }
  );

  return {
    controlnetUnits: controlnetUnitsParams,
  };
}

export function containsUrlWithoutQuery(list: string[], maybeInList: string) {
  function removeQuery(url: string) {
    return url.split("?").shift();
  }
  const withoutQuery = removeQuery(maybeInList);

  return !!list.find((u) => removeQuery(u) === withoutQuery);
}
