import { createContext, useContext, useRef } from "react";
import { TasksPollingManager } from "../../_utils/TasksPollingManager";

export type PollingManager = {
  tasksPollingManager: TasksPollingManager;
};

export const PollingManagerContext = createContext<PollingManager>({
  tasksPollingManager: new TasksPollingManager(),
});

type PollingManagerProviderProps = React.PropsWithChildren;
export function PollingManagerProvider({
  children,
}: PollingManagerProviderProps) {
  const contextValue = useRef({
    tasksPollingManager: new TasksPollingManager(),
  });

  return (
    <PollingManagerContext.Provider value={contextValue.current}>
      {children}
    </PollingManagerContext.Provider>
  );
}

export function usePollingManager() {
  return useContext(PollingManagerContext);
}
