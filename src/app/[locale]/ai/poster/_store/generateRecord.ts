import { makeAutoObservable, runInAction } from 'mobx';
import { RootStore } from '.';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { deleteRecords, fetchTaskList } from '@/api/aiPoster/task';
import { AIPoster, Fetch } from '@/api/types/aiPoster/task';

type RecordsStatus =
  // store中没有数据 也没有从服务端同步数据
  | 'init'
  // sore中没有数据 正在从服务端同步
  | 'loading'
  // store中有数据 正在从服务端同步
  | 'updating'
  // 同步完成
  | 'loaded'
  // 请求出错
  | 'error';

type FetchHistoryOptions = {
  enterStatus?: 'loading' | 'updating';
};

export class GenerateRecordStore {
  rootStore: RootStore;

  status: RecordsStatus = 'init';

  list = [] as AIPoster.Task[];

  public get successImagesCount() {
    return this.list.reduce((count, task) => {

      if (task.loadingStatus !== AIPoster.LoadingStatus.Success) {
        return count;
      }

      return (
        count +
        (task.resultImages?.filter(
          (image) => image.imageStatus === AIPoster.ImageStatus.Success
        ).length ?? 0)
      );
    }, 0);
  }

  async fetchHistory({ enterStatus = 'loading' }: FetchHistoryOptions) {
    const projectId = this.rootStore.projectsStore.activeProjectId;
    if (!projectId) {
      return;
    }

    let list: AIPoster.Task[] = [];
    try {
      runInAction(() => {
        this.status = enterStatus;
      });
      const res = await fetchTaskList({ projectId });
      list = res.list;
    } catch (e) {
      defaultErrorHandler(e);
      runInAction(() => {
        this.status = 'error';
      });
      return;
    }

    runInAction(() => {
      this.list = list;
      this.status = 'loaded';
    });
  }

  async delete(items: Fetch.DeleteRecordItem[]) {
    const projectId = this.rootStore.projectsStore.activeProjectId;
    if (!projectId) {
      return false;
    }

    const res = await deleteRecords({
      projectId,
      params: items
    });

    if (res.result) {
      await this.fetchHistory({ enterStatus: 'updating' });
    }

    return res.result;
  }

  constructor(rootStore: RootStore) {
    makeAutoObservable(this);

    this.rootStore = rootStore;
    const { tasksPollingManager } = rootStore.pollingManagers;
    tasksPollingManager.addTaskResolvedListener(() => {
      this.fetchHistory({ enterStatus: 'updating' });
    });
  }
}
