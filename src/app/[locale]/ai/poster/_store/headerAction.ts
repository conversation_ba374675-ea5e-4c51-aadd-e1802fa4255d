import { makeAutoObservable, runInAction } from "mobx";
import { RootStore } from ".";
import {
  MoveCanvasOfDefault,
  RenderCursor,
  CoreMouseMode,
} from "@meitu/whee-infinite-canvas";

/**
 * header中可选中的状态
 */
export enum HeaderAction {
  /**
   * 光标 -> 选择/拖拽
   */
  Cursor = "cursor",
  /**
   * 插入frame
   */
  InsertFrame = "insert-frame",
  /**
   * 插入文本
   */
  InsertText = "insert-text",
  /**
   * 改字
   */
  ModifyText = "modify-text",
  /**
   * 消除
   */
  Eraser = "eraser",
  /**
   * 改图
   */
  ModifyImage = "modify-image",
}

export enum CursorType {
  Select = "select",
  Drag = "drag",
}

export class HeaderActionStore {
  rootStore: RootStore;

  activeHeaderAction = HeaderAction.Cursor;
  public setActiveHeaderAction(action: HeaderAction, cursor?: CursorType) {
    this.activeHeaderAction = action;
    
    // 切换鼠标样式
    const renderStore = this.rootStore.renderStore;
    const renderStyle = renderStore.renderStyle;
    if (!renderStyle) {
      return;
    }
    switch(action) {
      case HeaderAction.InsertText: {
        return renderStyle.setCursorStyle({
          move: RenderCursor.insertText,
          hover: RenderCursor.insertText,
          defaults: RenderCursor.insertText,
        });
      }
      case HeaderAction.InsertFrame: {
        return renderStyle.setCursorStyle({
          move: RenderCursor.insertFrame,
          hover: RenderCursor.insertFrame,
          defaults: RenderCursor.insertFrame,
          mousedown: RenderCursor.insertFrame,
        });
      }
      case HeaderAction.Cursor: {
        return this._setActiveCursor(cursor ?? this.activeCursor);
      }
    }
  }

  activeCursor = CursorType.Select;

  public _setActiveCursor(cursor: CursorType) {
    this.activeCursor = cursor;

    const renderStore = this.rootStore.renderStore;
    const _setMove = () => {
      const renderStore = this.rootStore.renderStore;
      renderStore.renderStyle?.setCursorStyle({
        move: MoveCanvasOfDefault,
        hover: MoveCanvasOfDefault,
        defaults: MoveCanvasOfDefault,
        mousedown: RenderCursor.mousedown,
      });
    };

    const _setHover = () => {
      const renderStore = this.rootStore.renderStore;
      renderStore.renderStyle?.setCursorStyle({
        move: RenderCursor.default,
        hover: RenderCursor.hover,
        defaults: RenderCursor.default,
        mousedown: RenderCursor.default,
      });
    }

    if (cursor === CursorType.Select) {
      _setHover();
      renderStore.render?.unlockAllAndUnListenMove();
      renderStore.renderSelection?.setMode(CoreMouseMode.SELECTION);
      return;
    }

    if (cursor === CursorType.Drag) {
      _setMove();
      renderStore.render?.lockAllAndListenMove();
      return;
    }
  }

  public activateActionAndResetCursor(
    action: HeaderAction = HeaderAction.Cursor
  ) {
    this.setActiveHeaderAction(HeaderAction.Cursor, CursorType.Select);
    this.setActiveHeaderAction(action);
  }

  constructor(rootStore: RootStore) {
    makeAutoObservable(this);
    this.rootStore = rootStore;
  }
}
