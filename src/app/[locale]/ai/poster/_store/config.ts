import { makeAutoObservable, runInAction } from "mobx";
import { RootStore } from ".";
import { PosterConfigStore } from "@/stores/PosterConfigStore";
import { loadRemoteFont } from "@/utils/loadRemoteFont";
import { TemplateItemType } from "@/api/types/poster";


export class ConfigStore {
  activeCategoryId = 0;
  rootStore: RootStore;
  posterConfigStore: PosterConfigStore;

  countPerPage = 20;

  /**
   * 优先展示在对应分类之前的模版
   */
  attachTemplates: Map<number, Array<TemplateItemType>> = new Map();

  constructor(rootStore: RootStore, posterConfigStore: PosterConfigStore) {
    makeAutoObservable(this);

    this.rootStore = rootStore;
    this.posterConfigStore = posterConfigStore;
  }

  setActiveCategoryId(categoryId: number) {
    this.activeCategoryId = categoryId
  }

  /**
   * 获取配置
   */
  get config() {
    return this.posterConfigStore.config;
  }

  /**
   * 
   */
  get templateList() {
    // 将attachTemplates中的模版列表添加到对应分类下的前面
    return this.posterConfigStore.templateList.map((category) => {
      const attachList = this.attachTemplates.get(category.categoryId);
      if (!attachList) {
        return category;
      }

      const attachSet = new Set(attachList.map(t => t.id));
      const filteredList = attachSet.size ? category.list.filter(t => !attachSet.has(t.id)) : category.list;


      return {
        ...category,
        list: [...attachList, ...filteredList],
      }
    });
  }

  get currentCategoryIsLoading() {
    return this.activeCategoryId && this.posterConfigStore.isLoading(this.activeCategoryId);
  }

  get currentCategoryIsLastPage() {
    return this.posterConfigStore.isLastPage(this.activeCategoryId);
  }

  /**
   * 更新配置 加载默认字体
   * @param refresh 
   */
  async init(refresh: boolean = false) {
    if (!this.config || refresh) {
      this.attachTemplates.clear();
      await this.posterConfigStore.fetchConfig({ mode: 'refresh', count: this.countPerPage });
    }

    runInAction(() => {
      if (!this.config) {
        return;
      }

      const firstTab = this.config.template?.[0];
      if (firstTab?.categoryId) {
        this.setActiveCategoryId(firstTab.categoryId);
      }
    });

    if (!this.config) {
      return;
    }

    await Promise.allSettled([
      // 默认常规粗细的字体 由服务端下发
      loadRemoteFont(this.config.name, this.config.wofFontFile || this.config.fontFile),
      
      // 默认粗体的字体 前端写死
      loadRemoteFont("Roboto-Bold", "https://material-center.stariidata.com/material/font_subset/1743426733017.woff2"),  
    ]);
  }

  async fetchCurrentCategoryNextPage() {
    return await this.posterConfigStore.fetchConfig({ mode: 'append', categoryId: this.activeCategoryId, count: this.countPerPage });
  }

  async refreshCurrentCategory() {
    this.attachTemplates.set(this.activeCategoryId, []);
    return await this.posterConfigStore.fetchConfig({ mode: 'refresh', categoryId: this.activeCategoryId, count: this.countPerPage });
  }

  addAttachTemplates(template: TemplateItemType) {
    if (!template.categoryId) {
      return;
    }

    const list = this.attachTemplates.get(template.categoryId) || [];
    // 如果已经存在了 则不需要再次添加
    if (list.find(t => t.id === template.id)) {
      return;
    }

    this.attachTemplates.set(template.categoryId, [template, ...list]);
  }

  public findTemplate(categoryId: number, templateId: number) {
    return this.templateList.find(c => c.categoryId === categoryId)?.list.find(t => t.id === templateId);
  }
}
