import { makeAutoObservable, toJS } from "mobx";
import { RootStore } from ".";
import {
  BaseModelInfo,
  DraftType,
  EditorConfigModuleListResponse,
  StyleModelListQuery,
  StyleModelResponse,
} from "@/api/types/editorConfig";
import { defaultErrorHandler } from "@/utils/defaultErrorHandler";
import {
  fetchControlNetList,
  fetchPosterFont,
  fetchPosterModel,
  fetchStyleModelList,
} from "@/api/aiPoster/editor";
import { getAllModelByConfig, getModelById } from "../_utils/modelConfigUtils";
import { produce } from "immer";
import { PosterFontItemType } from "@/api/types/poster";

export class ParamsEditorStore {
  rootStore: RootStore;

  constructor(rootStore: RootStore) {
    makeAutoObservable(this);

    this.rootStore = rootStore;
  }

  // header 点击生图
  isOpenImageParams = false;
  setIsOpenImageParams(val: boolean) {
    this.isOpenImageParams = val;
  }

  // 会员转化弹窗
  openProjectMemberModal = false;
  setOpenProjectMemberModal = (val: boolean) => {
    this.openProjectMemberModal = val;
  };

  // 基础模型列表
  baseModelList: BaseModelInfo[] = [];
  setBaseModelList(val: BaseModelInfo[]) {
    this.baseModelList = val;
  }

  // 获取基础模型
  getBaseModelList() {
    return fetchPosterModel()
      .then((res) => {
        this.setBaseModelList(getAllModelByConfig(res?.baseModel || []));
        const firstModelId = res?.baseModel[0]?.list[0]?.id;
        // 初始化风格模型列表
        this.getStyleModelList({
          baseModelId: res.baseModel[0]?.list[0]?.id,
        });
        // 初始化controlNet列表
        this.getControlNetList(firstModelId);
      })
      .catch((e) => {
        defaultErrorHandler(e);
      });
  }

  // 画面参考模型
  controlNetList: EditorConfigModuleListResponse[] = [];
  setControlNetList(val: EditorConfigModuleListResponse[]) {
    this.controlNetList = val;
  }

  // 获取controlNet列表
  getControlNetList(baseModelId: number) {
    return fetchControlNetList({ baseModelId })
      .then((res) => {
        this.setControlNetList(res);
      })
      .catch((e) => {
        defaultErrorHandler(e);
      });
  }

  // 风格模型列表
  styleModelList: StyleModelResponse[] = [];
  setStyleModelList(val: StyleModelResponse[]) {
    this.styleModelList = val;
  }

  // 风格模型按钮图标
  addStyleBtnIcon = "";
  setAddStyleBtnIcon(val: string) {
    this.addStyleBtnIcon = val;
  }

  // 获取风格模型列表
  async getStyleModelList(
    params?: StyleModelListQuery & {
      reset?: boolean;
    }
  ) {
    const {
      categoryId,
      reset,
      keyword: keywordFromParams,
      baseModelId,
      ...leftParams
    } = params ?? {};

    const getStyleModelById = getModelById("categoryId", this.styleModelList);
    const matchedIndex = getStyleModelById(categoryId);

    const currentModel = this.styleModelList[matchedIndex] ?? null;

    if (currentModel && currentModel.cursor === currentModel.fetchingCursor) {
      return;
    }

    if (currentModel) {
      this.setStyleModelList(
        produce(toJS(this.styleModelList), (draft) => {
          draft[matchedIndex].fetchingCursor = currentModel.cursor;
        })
      );
    }

    const { styleModels, moreButtonLabel } = await fetchStyleModelList({
      categoryId,
      baseModelId,
      from: DraftType.AI_POSTER,
      cursor: reset ? undefined : currentModel?.cursor,
      keyword: keywordFromParams,
      ...leftParams,
    });
    this.setAddStyleBtnIcon(moreButtonLabel);

    const updateModelList = (models: StyleModelResponse[]) => {
      if (!currentModel) return models;

      return produce(toJS(this.styleModelList), (draft) => {
        const matchedModel = models.find(
          (model) => model.categoryId === categoryId
        );
        if (!matchedModel) return;

        draft[matchedIndex].list = reset
          ? matchedModel.list
          : [...draft[matchedIndex].list, ...matchedModel.list];
        draft[matchedIndex].cursor = matchedModel.cursor ?? "";
        draft[matchedIndex].fetchingCursor = undefined;
      });
    };

    this.setStyleModelList(updateModelList(styleModels));
  }

  // 字体列表
  familyList: PosterFontItemType[] = [];
  setFamilyList(val: PosterFontItemType[]) {
    this.familyList = val;
  }

  // 获取字体
  getFamilyList() {
    return fetchPosterFont()
      .then((res) => {
        this.setFamilyList(res.list);
      })
      .catch((e) => {
        defaultErrorHandler(e);
      });
  }
}
