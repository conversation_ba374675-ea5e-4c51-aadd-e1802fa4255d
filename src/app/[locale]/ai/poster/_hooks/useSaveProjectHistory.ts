import { RootStore } from "../_store";
import {
  DeltaType,
  ElementOptions,
  FabricObject,
  HistoryChangeType,
  Operation,
  Render,
} from "@meitu/whee-infinite-canvas";
import { deleteElements, saveElements } from "../_utils/saveProjectHistory";
import { useGuideCreateProject } from "../_context/GuideCreateProjectContext";
import { MEMBER_EXPIRED_CODE, MEMBER_EXPIRED_CODE_PLUS } from "@/constants/errorCode";

//处理画布历史redo undo 提交项目历史参数
export const handleHistoryOperationsParams = ({
  operations,
  historyChangeType,
}: {
  operations: Operation[];
  historyChangeType: HistoryChangeType;
}) => {
  const deleteOptions: ElementOptions[] = [];
  const saveOptions: ElementOptions[] = [];
  operations.forEach((operation) => {
    const objects =
      operation.options.preData?.objects ??
      operation.options.afterData?.objects ??
      [];
    if (historyChangeType === HistoryChangeType.UNDO) {
      if (operation.options.type === DeltaType.MODIFY) {
        saveOptions.push(...(operation.options.preData?.objects ?? []));
      } else if (operation.options.type === DeltaType.INSERT) {
        deleteOptions.push(...objects);
      } else {
        saveOptions.push(...objects);
      }
    } else if (historyChangeType === HistoryChangeType.REDO) {
      if (operation.options.type === DeltaType.MODIFY) {
        saveOptions.push(...(operation.options.afterData?.objects ?? []));
      } else if (operation.options.type === DeltaType.DELETE) {
        deleteOptions.push(...objects);
      } else {
        saveOptions.push(...objects);
      }
    }
  });
  return { deleteOptions, saveOptions };
};

export const useSaveCanvas = (rootStore: RootStore) => {

  const { createGuideProject, isInGuidePage } = useGuideCreateProject();

  const submitSaveElements = async (
    targets: FabricObject[],
    operations?: ElementOptions[]
  ) => {
    const render = rootStore.renderStore.render;
    const projectId = rootStore.projectsStore.activeProjectId;
    if (!render) {
      return;
    };

    if (!projectId) {
      if (!isInGuidePage) {
        return;
      }

      return await createGuideProject();
    }

    try {
      return await saveElements({
        targets,
        operations,
        render,
        projectId,
      });
    } catch(e: any) {
      if (e?.response?.data?.code === MEMBER_EXPIRED_CODE || e?.response?.data?.code === MEMBER_EXPIRED_CODE_PLUS) {
        if (projectId !== rootStore.projectsStore.activeProjectId) {
          return;
        }
        rootStore.projectsStore.disableEditable();
      }

      throw e;
    }
  };

  const submitDeleteElements = async (
    targets: FabricObject[],
    operations?: ElementOptions[]
  ) => {
    const render = rootStore.renderStore.render;
    const projectId = rootStore.projectsStore.activeProjectId;
    if (!render) return;

    if (!projectId) {
      return await createGuideProject();
    }

    try {
      return deleteElements({
        targets,
        operations,
        render,
        projectId,
      });
    } catch(e: any) {
      if (e?.response?.data?.code === MEMBER_EXPIRED_CODE || e?.response?.data?.code === MEMBER_EXPIRED_CODE_PLUS) {
        if (projectId !== rootStore.projectsStore.activeProjectId) {
          return;
        }
        rootStore.projectsStore.disableEditable();
      }

      throw e;
    }
  };

  return { submitSaveElements, submitDeleteElements };
};


export function useSubmitAddHistory(rootStore: RootStore) {

  const { renderStore } = rootStore;
  const { submitSaveElements } = useSaveCanvas(rootStore);

  const submitAddHistory = (objects: FabricObject[]) => {
    const { historyPlugins, render } = renderStore;
    if (!historyPlugins || !render) {
      return;
    }
    const operation = historyPlugins.baseAction.getAddOperation({
      objects
    });
    if (!operation) return;
    historyPlugins.submit(operation);
    return submitSaveElements(objects);
  };

  return submitAddHistory;
}
