import {
  Group,
  DeltaType,
  FabricObject,
  Point,
  ElementName,
  Operation,
  createContainerElements,
  ActiveSelection,
  AlignMode,
  Canvas,
} from "@meitu/whee-infinite-canvas";
import { useSaveCanvas } from "./useSaveProjectHistory";
import { RootStore } from "../_store";

const useAction = (rootStore: RootStore) => {
  const {
    renderStore: { render, historyPlugins },
  } = rootStore;
  const { submitSaveElements, submitDeleteElements } = useSaveCanvas(rootStore);

  const pasteAction = async (point: Point, parent?: Group | Canvas) => {
    const elements = await render?.Actions.paste(point, true, parent);
    if (!elements) return;
    const operation = historyPlugins?.baseAction.getAddOperation({
      objects: elements,
    });
    if (!operation) return;
    historyPlugins?.submit(operation);
    submitSaveElements(elements);
  };

  const deleteAction = (targets: FabricObject[]) => {
    const operation = historyPlugins?.baseAction.getRemoveOperation({
      objects: targets,
    });
    if (!operation) return;
    historyPlugins?.submit(operation);
    render?.Actions.remove(targets);
    submitDeleteElements(targets);
  };

  const cutAction = (targets: FabricObject[]) => {
    const operation = historyPlugins?.baseAction.getRemoveOperation({
      objects: targets,
    });
    if (!operation) return;
    historyPlugins?.submit(operation);
    render?.Actions.cut(targets);
    submitDeleteElements(targets);
  };

  const bringToFrontAction = (targets: FabricObject[]) => {
    const operation = historyPlugins?.baseAction.getZIndexOperation(
      targets,
      DeltaType.FRONT
    );
    if (!operation) return;
    historyPlugins?.submit(operation);
    render?.Actions.bringToFront(targets);
    submitSaveElements(targets);
  };

  const sendToBackAction = (targets: FabricObject[]) => {
    const operation = historyPlugins?.baseAction.getZIndexOperation(
      targets,
      DeltaType.BACK
    );
    if (!operation) return;
    historyPlugins?.submit(operation);
    render?.Actions.sendToBack(targets);
    submitSaveElements(targets);
  };

  const bringForwardAction = (targets: FabricObject[]) => {
    const operation = historyPlugins?.baseAction.getZIndexOperation(
      targets,
      DeltaType.FORWARD
    );
    if (!operation) return;
    historyPlugins?.submit(operation);
    render?.Actions.bringForward(targets);
    submitSaveElements(targets);
  };

  const sendBackwardAction = (targets: FabricObject[]) => {
    const operation = historyPlugins?.baseAction.getZIndexOperation(
      targets,
      DeltaType.BACKWARD
    );
    if (!operation) return;
    historyPlugins?.submit(operation);
    render?.Actions.sendBackward(targets);
    submitSaveElements(targets);
  };

  const mergeAction = (targets: FabricObject[]) => {
    const isFrameInEls = targets.some((el) => el._name_ === ElementName.FRAME);
    if (isFrameInEls) return;
    const operations: Operation[] = [];
    targets.forEach((el) => {
      const operation = historyPlugins?.baseAction.getRemoveOperation({
        objects: [el],
      });
      if (!operation) return;
      operations.push(operation);
      if (el.group) {
        el.group.remove(el);
      }
      render?._FC.remove(el);
    });
    const container = createContainerElements("", targets);
    render?._FC.add(container);
    render?._FC.setActiveObject(container);
    render?._FC.requestRenderAll();
    const operation = historyPlugins?.baseAction.getAddOperation({
      objects: [container],
    });
    if (!operation) return;
    operations.push(operation);
    historyPlugins?.submit(operations);
    submitSaveElements([container]);
  };

  const splitAction = (targets: FabricObject[]) => {
    const operations: Operation[] = [];
    const container = targets[0] as Group;
    const objects = container.getObjects();
    objects.forEach((object) => {
      object.set({
        _parent_id_: "",
      });
      render?._FC.add(object);
      const operation = historyPlugins?.baseAction.getAddOperation({
        objects: [object],
      });
      if (!operation) return;
      operations.push(operation);
    });
    const operation = historyPlugins?.baseAction.getRemoveOperation({
      objects: [container],
    });
    if (!operation) return;
    operations.push(operation);
    if (container.group) {
      container.group.remove(container);
    }
    render?._FC.remove(container);
    container.remove(...objects);
    const as = new ActiveSelection(objects, {
      canvas: render?._FC,
    });
    render?._FC.setActiveObject(as);
    render?._FC.requestRenderAll();
    historyPlugins?.submit(operations);
    submitDeleteElements([container]);
    submitSaveElements(objects);
  };

  const alignAction = (mode: AlignMode) => {
    const modifyActiveObject = render?._FC.getActiveObject();
    if (!modifyActiveObject) return;
    const beforeData =
      historyPlugins?.baseAction.getElementData({
        target: modifyActiveObject,
      }) ?? [];
    render?.Actions?.alignMode(mode);
    const modifiedActiveObject = render?._FC.getActiveObject();
    if (!modifiedActiveObject) return;
    const afterData =
      historyPlugins?.baseAction.getElementData({
        target: modifiedActiveObject,
      }) ?? [];
    const operation = historyPlugins?.baseAction.getModifiedOperation({
      beforeData,
      afterData,
    });
    if (!operation) return;
    historyPlugins?.submit(operation);

    const container = modifiedActiveObject as Group;
    const objects = container.getObjects();
    submitSaveElements(objects);
  };

  return {
    pasteAction,
    deleteAction,
    cutAction,
    bringToFrontAction,
    sendToBackAction,
    mergeAction,
    splitAction,
    bringForwardAction,
    sendBackwardAction,
    alignAction,
  };
};

export default useAction;
