import { useEffect, useRef } from "react";
import { driver, Driver } from "driver.js";
import "./index.scss";
import { useI18n } from "@/locales/client";
import { RightSiderTabsKey } from "../../_store/editorStatus";
import { RootStore } from "../../_store";
import { insertImageToViewPortCenter } from "../../_utils/insertImage";
import { Group } from "@meitu/whee-infinite-canvas";
import { useParamsSidebarContext } from "../../_components/ParamsSidebar/context";

const TutorialsKey = "poster-editor/tutorials";
const PosterDemoUrl =
  "https://wheeai.meitudata.com/static/686cb52a32733258xUaArAkTf7095.jpg";

export const useTutorials = (
  loading: boolean,
  rootStore: RootStore,
  onInitialized?: (driver: Driver) => void
) => {
  const t = useI18n();
  const driverObjRef = useRef<Driver | null>(null);
  const eleRef = useRef<Group | null>(null);
  const { openImageEditSwitch } = useParamsSidebarContext();

  useEffect(() => {
    // 是从创建页进入，非loading，当前uid没有打开过
    if (!loading && localStorage.getItem(TutorialsKey) !== "true") {
      initTutorials();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading]);

  const handleClose = () => {
    // 引导完成，移除demo图
    if (eleRef.current) {
      rootStore.renderStore.render?.Actions.remove([eleRef.current]);
    }
    rootStore.editorStatusStore.setRightSiderActiveTab(
      RightSiderTabsKey.Template
    );

    driverObjRef.current?.destroy();

    localStorage.setItem(TutorialsKey, "true");
  };

  const initTutorials = () => {
    const driverObj = driver({
      animate: true,
      showButtons: ["next", "close"],
      showProgress: true,
      allowClose: true,
      nextBtnText: "下一步",
      doneBtnText: "完成",
      popoverClass: "poster-tutorials-popover",
      stageRadius: 12,
      stagePadding: 0,
      popoverOffset: 16,
      onDestroyed: () => {
        handleClose();
      },
      onCloseClick: () => {
        handleClose();
      },
      steps: [
        {
          element: "#poster-prompt",
          popover: {
            title: "海报生成",
            description: "在这里输入提示词，可以得到你想要的海报哦～",
            progressText: "1/5",
            side: "right",
            align: "start",
            onNextClick: async () => {
              if (!rootStore.renderStore.render) return;
              const ele = await insertImageToViewPortCenter(
                {
                  src: PosterDemoUrl,
                  _custom_data_history_: {
                    img2img: true,
                  },
                },
                rootStore.renderStore.render
              );
              eleRef.current = ele;
              rootStore.renderStore.render?.backToOriginPosition();
              // 打开图片编辑
              openImageEditSwitch({ shape: ele });
              driverObj.moveNext();
            },
          },
        },
        {
          element: "#poster-params-section",
          popover: {
            title: "海报修改",
            description:
              "在框内输入修改内容，可以得到修改后的海报，使用下方修改模版，效果更佳哦～",
            progressText: "2/5",
            side: "right",
            align: "start",
          },
        },
        {
          element: "#poster-right-tabs",
          popover: {
            title: "海报灵感",
            description:
              "没灵感？来海报灵感看看吧～tip：可直接在图像修改区域修改灵感海报，以获得满意的海报哦",
            progressText: "3/5",
            side: "left",
            align: "start",
          },
        },
        {
          element: "#poster-image-actions",
          popover: {
            title: "海报编辑工具",
            description: "对海报不满意，试试上方的海报编辑工具～",
            progressText: "4/5",
            side: "bottom",
            align: "start",
            onNextClick: () => {
              rootStore.editorStatusStore.setRightSiderActiveTab(
                RightSiderTabsKey.History
              );
              driverObj.moveNext();
            },
          },
        },
        {
          element: "#poster-right-tabs",
          popover: {
            title: "历史记录",
            description: "生成的图像可以在【历史记录】查看哦～",
            progressText: "5/5",
            side: "left",
            align: "start",
          },
        },
      ],
    });
    driverObjRef.current = driverObj;
    // 初始化完成后调用回调
    onInitialized?.(driverObj);
    driverObj.drive();
  };

  return driverObjRef;
};
