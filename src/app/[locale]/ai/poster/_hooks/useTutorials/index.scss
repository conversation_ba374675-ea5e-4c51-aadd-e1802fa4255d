.poster-tutorials-popover {
  &.driver-popover {
    width: 280px;
    padding: 16px;
    border-radius: var(--radius-12, 12px);
    background: #fff;
  }

  .driver-popover-close-btn {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--system-background-secondary, #F1F5F9);
    top: 16px;
    right: 16px;
    color: #293545;
    font-weight: normal;
  }

  .driver-popover-arrow {
    display: none;
  }

  .driver-popover-title {
    color: var(--system-content-secondary, #293545);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
  }

  .driver-popover-description {
    color: var(--system-content-thirdary, #6A7B94);
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    margin-top: 8px;
  }

  .driver-popover-footer {
    margin-top: 16px;

    .driver-popover-progress-text {
      color: var(--system-content-secondary, #293545);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      letter-spacing: var(--letterSpacing-md, 0px);
    }

    .driver-popover-navigation-btns {
      .driver-popover-next-btn {
        border-radius: 8px;
        background: var(--background-btnAi, #3549FF);
        padding: 0 10px;
        height: 34px;
        color: var(--content-btnPrimary, #FFF);
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        width: 100px;
        text-align: center;
        text-shadow: none;
        border: 0;

        &:hover {
          background: #3042e5;
        }
      }
    }
  }
}