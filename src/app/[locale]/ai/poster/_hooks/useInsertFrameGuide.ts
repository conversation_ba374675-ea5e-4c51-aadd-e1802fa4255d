import { useI18n } from "@/locales/client";
import { ConfigStore } from "../_store/config";
import { RenderStore } from "../_store/render";
import { insertFrameGuide, InsertFrameGuideOptions } from "../_utils/frame-guide";
import _ from "lodash";

type UseInsertFrameGuideDeps = {
  configStore: ConfigStore,
  renderStore: RenderStore,
}

export function useInsertFrameGuide({
  configStore,
  renderStore
}: UseInsertFrameGuideDeps) {
  const t = useI18n();

  return async function (options: Pick<InsertFrameGuideOptions, 'frame'>) {
    const render = renderStore.render;
    if (!render) {
      return;
    }

    return await insertFrameGuide(render, {
      frame: options.frame,
      guide: {
        marginRight: 32,
        width: 308,
        title: {
          text: t("frame-guide.title"),
          fontSize: 20,
          fontFamily:   configStore.config?.name ?? "",
        },

        steps: [
          {
            marginTop: 24,
            marginLeft: 8,
            content: {
              marginTop: 3,
              text: t("frame-guide.step1"),
              fontFamily:   configStore.config?.name ?? "",
              fontSize: 12,
            }
          },
          ..._.range(2, 6).map(i => {
            return {
              marginTop: 16,
              marginLeft: 8,
              content: {
                marginTop: 3,
                text: t(`frame-guide.step${i}` as 'frame-guide.step1'),
                fontFamily:   configStore.config?.name ?? "",
                fontSize: 12,
              }
            }
          })
        ],

        tips: {
          text: t("frame-guide.tips"),
          fontFamily:   configStore.config?.name ?? "",
          fontSize: 12,
          marginTop: 16,
        },

        previewImage: {
          scale: 0.5,
          marginTop: 24,
        }
      }
    });
  }
}