import { useEffect } from "react";
import { useRootStore } from "../_store";
import { HeaderAction } from "../_store/headerAction";


export function useEnsureSelectSingleImage() {
  const { headerActionStore, selectionStore } = useRootStore();
  const { singleImage } = selectionStore;

  useEffect(() => {
    if (!singleImage) {
      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
    }
  }, [singleImage])

  return { singleImage }
}
