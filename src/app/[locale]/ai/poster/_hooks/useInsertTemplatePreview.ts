import { TemplateItemType } from "@/api/types/poster";
import { useSaveCanvas } from "./useSaveProjectHistory";
import { createImage } from "@meitu/whee-infinite-canvas";
import { RootStore } from "../_store";

// 插入模版预览图
export function useInsertTemplatePreview(rootStore: RootStore) {
  const { submitSaveElements } = useSaveCanvas(rootStore);

  return async (template: TemplateItemType) => {
    const renderStore = rootStore.renderStore;
    const historyPlugins = renderStore.historyPlugins;
    const render = renderStore.render;

    if (!render || !historyPlugins) return;

    const group = await createImage("", {
      src: template.pic,
      // 默认打开图生图开关
      _custom_data_history_: {
        img2img: true,
      },
    });

    if (!group) return;

    render.addToViewPortCenterByArrangement(group);
    render._FC.setActiveObject(group);
    render.backToOriginPosition({ target: group });
    render._FC.requestRenderAll();

    const operation = historyPlugins?.baseAction.getAddOperation({
      objects: [group],
    });
    if (operation) {
      historyPlugins?.submit(operation);
    }
    submitSaveElements([group]);

    return group;
  };
}
