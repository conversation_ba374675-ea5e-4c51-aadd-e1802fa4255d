import { useEffect, useRef } from "react";

import useAction from "./useAction";
import {
  newElementOffset,
  Point,
  ElementName,
} from "@meitu/whee-infinite-canvas";
import { RootStore } from "@/app/[locale]/ai/poster/_store";
import { isTextEditing } from "../_utils/isTextEditing";
import zoomToFitCanvas from "../_utils/zoomToFitCanvas";
import { CursorType, HeaderAction } from "../_store/headerAction";

const useRegisterHotKey = (rootStore: RootStore) => {
  const {
    renderStore: { renderHotkey, historyPlugins, render },
    paramsEditorStore,
    editorStatusStore,
    headerActionStore,
  } = rootStore;
  const {
    pasteAction,
    deleteAction,
    cutAction,
    mergeAction,
    sendToBackAction,
    bringToFrontAction,
    splitAction,
    bringForwardAction,
    sendBackwardAction,
  } = useAction(rootStore);

  const isClearUIRef = useRef(false);

  // 抽出所有快捷键处理函数
  const handleUndo = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    historyPlugins?.undo();
  };

  const handleRedo = (event: KeyboardEvent) => {
    event.preventDefault();
    if (isTextEditing(render)) return;
    historyPlugins?.redo();
  };

  const handleDelete = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    const targets = render?._FC.getActiveObjects();
    if (!targets?.length) return;
    event.preventDefault();
    deleteAction(targets);
  };

  const handleCopy = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    const targets = render?._FC.getActiveObjects();
    if (!targets?.length) return;
    render?.Actions?.copyStash(...targets);
  };

  const handlePaste = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    const pointer = render?.Actions?._copyPoint;
    if (!pointer) return;
    pasteAction(
      new Point(pointer.x + newElementOffset, pointer.y + newElementOffset)
    );
  };

  const handleCut = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    const targets = render?._FC.getActiveObjects();
    if (!targets) return;
    cutAction(targets);
  };

  const handleSendToBack = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    const targets = render?._FC.getActiveObjects();
    if (!targets) return;
    sendToBackAction(targets);
  };

  const handleBringToFront = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    const targets = render?._FC.getActiveObjects();
    if (!targets) return;
    bringToFrontAction(targets);
  };

  const handleMerge = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    const targets = render?._FC.getActiveObjects();
    if (!targets) return;
    const isFrameInEls = targets?.some((el) => el._name_ === ElementName.FRAME);
    if ((targets?.length ?? 0) < 2 || isFrameInEls) return;
    mergeAction(targets);
  };

  const handleSplit = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    const targets = render?._FC.getActiveObjects();
    if (!targets) return;
    const isContainer =
      targets?.length === 1 && targets[0]?._name_ === ElementName.CONTAINER;
    if (!isContainer) return;
    splitAction(targets);
  };

  const handleSendBackward = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    const targets = render?._FC.getActiveObjects();
    if (!targets) return;
    sendBackwardAction(targets);
  };

  const handleBringForward = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    const targets = render?._FC.getActiveObjects();
    if (!targets) return;
    bringForwardAction(targets);
  };

  const handleZoom100 = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    render?.setZoom(1);
  };

  const handleFitScreen = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    if (!render) return;
    zoomToFitCanvas(render);
  };

  const handleBackToOrigin = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    render?.backToOriginPosition();
  };

  const handleZoomIn = (event: KeyboardEvent) => {
    event.preventDefault();
    if (isTextEditing(render)) return;
    const zoom = render?._FC?.getZoom() ?? 1;
    let newZoom = zoom + zoom * 0.1;
    if (newZoom >= 20) {
      newZoom = 20;
    }
    render?.setZoom(newZoom);
    render?._FC?.fire("viewport:zoom", { zoom: newZoom });
  };

  const handleZoomOut = (event: KeyboardEvent) => {
    event.preventDefault();
    if (isTextEditing(render)) return;
    const zoom = render?._FC?.getZoom() ?? 1;
    let newZoom = zoom - zoom * 0.1;
    if (newZoom <= 0.1) {
      newZoom = 0.1;
    }
    render?.setZoom(newZoom);
    render?._FC?.fire("viewport:zoom", { zoom: newZoom });
  };

  const handleUploadImage = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    const wrapper = document.querySelector(`.poster-editor-uploader`);
    const input = wrapper?.querySelector("input");
    input?.click();
  };

  const handleDisableSave = (event: KeyboardEvent) => {
    event.preventDefault();
  };

  const handleOpenImageParams = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    render?._FC.discardActiveObject();
    paramsEditorStore.setIsOpenImageParams(true);
  };

  const handleClearScreenUI = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    editorStatusStore.setIsClearScreenUI(!isClearUIRef.current);
    isClearUIRef.current = !isClearUIRef.current;
  };

  const handleInsertText = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    headerActionStore.setActiveHeaderAction(HeaderAction.InsertText);
  };

  const handleInsertFrame = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    headerActionStore.setActiveHeaderAction(HeaderAction.InsertFrame);
  };

  const handleActivateAction = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    headerActionStore.activateActionAndResetCursor();
  };

  const handleDragCursor = (event: KeyboardEvent) => {
    if (isTextEditing(render)) return;
    event.preventDefault();
    headerActionStore.setActiveHeaderAction(HeaderAction.Cursor);
    headerActionStore.setActiveHeaderAction(
      HeaderAction.Cursor,
      CursorType.Drag
    );
  };

  const handleWheel = (event: WheelEvent) => {
    if (event.ctrlKey) {
      event.preventDefault();
    }
  };

  useEffect(() => {
    if (!renderHotkey || !historyPlugins) return;

    // 注册所有快捷键
    const hotkeyMap = {
      "ctrl+z,command+z": handleUndo,
      "ctrl+y,command+y": handleRedo,
      backspace: handleDelete,
      "ctrl+c,command+c": handleCopy,
      "ctrl+v,command+v": handlePaste,
      "ctrl+x,command+x": handleCut,
      "[": handleSendToBack,
      "]": handleBringToFront,
      "ctrl+g,command+g": handleMerge,
      "ctrl+backspace,command+backspace": handleSplit,
      "ctrl+[,command+[": handleSendBackward,
      "ctrl+],command+]": handleBringForward,
      "ctrl+0,command+0": handleZoom100,
      "shift+1": handleFitScreen,
      "shift+2": handleBackToOrigin,
      "ctrl+=,command+=": handleZoomIn,
      "ctrl+-,command+-": handleZoomOut,
      u: handleUploadImage,
      "ctrl+s,command+s": handleDisableSave,
      // 'G': handleOpenImageParams,
      // 'ctrl+\\,command+\\': handleClearScreenUI,
      // 't': handleInsertText,
      // 'f': handleInsertFrame,
      s: handleActivateAction,
      h: handleDragCursor,
    };

    // 注册所有快捷键
    Object.entries(hotkeyMap).forEach(([key, handler]) => {
      renderHotkey.registerHotKey(key, handler);
    });

    // 注册滚轮事件
    document.addEventListener("wheel", handleWheel, { passive: false });

    // 卸载所有快捷键和事件
    return () => {
      // 卸载所有快捷键
      Object.entries(hotkeyMap).forEach(([key, handler]) => {
        renderHotkey.unregisterHotKey(key, handler);
      });

      // 卸载滚轮事件
      document.removeEventListener("wheel", handleWheel);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [renderHotkey, historyPlugins]);
};

export default useRegisterHotKey;
