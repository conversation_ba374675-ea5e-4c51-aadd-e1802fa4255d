.sweep-loading {
  display: flex;
  height: 100%;
  width: 100%;
  position: relative;
  background: #EDEDEF;

  @keyframes sweepLight {
    0% {
      left: -100%;
    }
    100% {
      left: 200%;
    }
  }

  &::before {
    content: "";
    position: absolute;
    width: 25px;
    height: 200%;
    top: -50%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 100%);
    transform: rotate(45deg);
    
    & :local {
      animation: sweepLight 2s infinite;
    }
  }
}