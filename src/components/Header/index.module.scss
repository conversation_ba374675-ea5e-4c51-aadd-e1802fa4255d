.header {
  display: grid;
  height: 48px;
  grid-template-columns: repeat(3, 1fr);
  overflow: hidden;
  border-bottom: 1px solid var(--system-stroke-input, #E2E8F0);
  background: var(--editor-background);
  user-select: none;
  box-sizing: border-box;

  :global {
    .slot {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;

      &.left {
        justify-content: flex-start;
      }

      &.center {
        justify-content: center;
      }

      &.right {
        justify-content: flex-end;
      }
    }
  }
}