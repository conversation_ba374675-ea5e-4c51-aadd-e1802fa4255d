.accountAvatar {
  display: inline-flex;
  align-items: center;
  padding: 4px 0;
  border-radius: 6px;
  line-height: 1;
  transition: background-color 0.2s;
  cursor: pointer;
  user-select: none;
  position: relative;
  margin-right: 16px;

  :global .ant-avatar {
    border-color: #d0d2d6;
    background: #f6f7fa;
    color: #abadb2;

    .action {
      font-size: 16px;
    }

    +strong {
      margin-left: 8px;
    }
  }
}

.dropdownOverlay {
  width: 228px;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0px 10px 40px 0px rgba(0, 0, 0, 0.1),
    0px 0px 2px 0px rgba(0, 0, 0, 0.08);

  &:global(.ant-dropdown) :global .ant-dropdown-menu {
    box-shadow: none;

    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu {
      &:local(.logout) {
        padding-top: 8px;
        padding-bottom: 8px;
        text-align: center;

        :global(.ant-dropdown-menu-submenu-expand-icon) {
          display: none;
        }
      }
    }

    .ant-dropdown-menu-submenu-title {
      padding-right: 12px;
    }
  }

  .account {
    display: flex;
    padding: 12px 12px 8px;
    cursor: default;

    &:hover {
      background: transparent;
    }

    :global .ant-avatar {

      &-lg {
        width: 44px;
        height: 44px;
      }
    }

    .accountProfile {
      display: flex;
      flex-direction: column;
      flex: auto;
      width: 1px;
      margin-left: 12px;

      strong {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 500;
        line-height: 1.****************;
        font-size: 14px;
        color: #1c1d1f;
      }

      small {
        font-size: 12px;
        line-height: 1.****************;
        color: #abadb2;
      }
    }
  }

  .feedback {
    :global .ant-dropdown-menu-title-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      height: 30px;
      font-weight: 600;
    }
  }

  .memberDesc {
    background-image: url('./container.jpg');
    padding: 12px 8px !important;
    margin: 0 8px !important;
    border-radius: 8px !important;
    background-size: cover;

    .memberCard {
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-between;
      align-items: center;

      .left {
        display: flex;
        flex-direction: column;
        gap: 6px;

        .logo {
          display: flex;
          gap: 3px;
        }

        .text {
          color: var(--content-on-vip-btn, #3c2404);
          font-size: 12px;
          line-height: 16px;
        }
      }

      .right {
        padding: 0px 12px !important;
        height: 22px !important;
        display: flex;
        align-items: center;

        color: var(--content-on-vip-btn, #3c2404) !important;
        font-size: 12px !important;
        line-height: 16px;

        border: none;
        border-radius: 8px !important;
        background: var(--background-whee-vip-btn,
            linear-gradient(270deg, #ffd281 0%, #ffebbc 100%)) !important;
      }
    }
  }

  :global .ant-badge {
    line-height: inherit !important;
  }
}