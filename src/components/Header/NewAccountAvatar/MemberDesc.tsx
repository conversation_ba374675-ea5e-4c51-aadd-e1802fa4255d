"use client";
import { useMemberShipDesc } from "@/hooks/useMember";
import styles from "./index.module.scss";
import { MemberLogo, VipLogo } from "./icons";
import { useMemo } from "react";
import { MembershipDescResponse, MemberStatus } from "@/types";
import { VipButton } from "./VipButton";
import { observer } from "mobx-react";

export const assertUnreachable = (
  value: never,
  errorMessage?: string | undefined
) => {
  throw new Error(
    errorMessage || `Unreachable value received: ${JSON.stringify(value)}`
  );
};

const MemberDesc = () => {
  const { membershipDesc } = useMemberShipDesc();

  if (!membershipDesc || membershipDesc.status === MemberStatus.DISABLED)
    return null;

  return <MemberDescCard membershipDesc={membershipDesc} />;
};

export default observer(MemberDesc);

export interface MemberDescCardProps {
  membershipDesc: MembershipDescResponse;
}

const MemberDescCard = ({ membershipDesc }: MemberDescCardProps) => {
  const isShowButton = membershipDesc.status !== MemberStatus.NORMAL;

  const buttonName = useMemo(() => {
    switch (membershipDesc.status) {
      case MemberStatus.NOT_MEMBER:
        return "开通会员";
      case MemberStatus.EXPIRED:
        return "续费";
      case MemberStatus.DISABLED:
      case MemberStatus.NORMAL:
        return "";

      default:
        assertUnreachable(membershipDesc.status, "Invalid member status");
    }
  }, [membershipDesc.status]);

  return (
    <div className={styles.memberCard}>
      <div className={styles.left}>
        <div className={styles.logo}>
          <MemberLogo />
          <VipLogo />
        </div>
        <span className={styles.text}>{membershipDesc.desc}</span>
      </div>
      {isShowButton && (
        <VipButton isVip size="small" className={styles.right}>
          {buttonName}
        </VipButton>
      )}
    </div>
  );
};
