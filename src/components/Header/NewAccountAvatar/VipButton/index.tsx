import classNames from "classnames";
import styles from "./index.module.scss";
import { ButtonProps, ConfigProvider } from "antd";
import { Button } from "@/components/Button";

export type VipButtonType = ButtonProps & {
  isVip?: boolean;
};

export function VipButton({
  className,
  isVip = true,
  ...restProps
}: VipButtonType) {
  return (
    // 去除两个中文字符中间的空格
    <ConfigProvider autoInsertSpaceInButton={false}>
      <Button
        {...restProps}
        className={classNames(
          {
            [styles.vipButton]: isVip,
          },
          className
        )}
      />
    </ConfigProvider>
  );
}
