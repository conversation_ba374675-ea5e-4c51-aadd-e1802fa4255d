"use client";

import type { MenuProps } from "antd";
import { <PERSON><PERSON><PERSON>, Dropdown, <PERSON><PERSON>, Badge } from "antd";
import { UserBoldOutlined } from "@meitu/candy-icons";
import { Link } from "react-router-dom";
import styles from "./index.module.scss";
import { useEffect, useMemo, useState } from "react";
import MemberDesc from "./MemberDesc";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import { MemberGroupCategory } from "@/types";
import { defaultErrorHandler } from "@/utils/defaultErrorHandler";
import { toAtlasImageView2URL } from "@meitu/util";
import { useStore } from "@/contexts/StoreContext";
import { clearAccount, wheeLoginPopup } from "@/utils/account";
import { getConfig } from "@/config";
import { unReadMessageCount } from "@/api/account";
import { AvatarMenuItemKey } from "./constants";
import { observer } from "mobx-react";
import { useRootStore } from "@/app/[locale]/editor/_store";

const config = getConfig();

let timer: number | null = null;

function AccountAvatar() {
  const [open, setOpen] = useState(false);

  const { userStore } = useStore();
  const { editorStatusStore } = useRootStore();
  const isLogin = userStore.isLogin;
  const user = userStore.currentUser;
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();

  const [onlineUnReadCount, setOnlineUnReadCount] = useState(0);

  useEffect(() => {
    getUnReadCount();
    getPollingNumberOfUnreadMsg(20000);

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getUnReadCount = () => {
    unReadMessageCount({
      client_id: config.ACCOUNT_CLIENT_ID,
    }).then((res) => {
      setOnlineUnReadCount(res.onlineUnreadNum);
    });
  };

  // 轮询-获取未读消息数量
  const getPollingNumberOfUnreadMsg = async (delay: number) => {
    try {
      timer = window.setInterval(async function () {
        getUnReadCount();
      }, delay);
    } catch (error) {
      defaultErrorHandler(error);
    }
  };

  const openOnlineConsultationModal = () => {
    editorStatusStore.setShowConsultationModal(true);
  };

  const menuItems = useMemo<NonNullable<MenuProps["items"]>>(() => {
    return [
      {
        key: "member-desc",
        className: styles.memberDesc,
        label: <MemberDesc />,
        onClick() {
          openMeiDouRecordsPopup(MemberGroupCategory.Member);
        },
      },
      {
        key: AvatarMenuItemKey.Personal,
        className: styles.logout,
        label: (
          <a href="/personal" target="_blank">
            个人主页
          </a>
        ),
        onClick(key) {
          setOpen(false);
        },
      },

      {
        key: AvatarMenuItemKey.OnlineConsultation,
        className: styles.logout,
        label: (
          <Badge
            dot
            color="#F74640"
            offset={[10, 10]}
            count={onlineUnReadCount}
          >
            <span>在线咨询</span>
          </Badge>
        ),
        onClick(key) {
          // 未读数量置0
          setOnlineUnReadCount(0);

          openOnlineConsultationModal();
          setOpen(false);
        },
      },
      {
        type: "divider",
      },
      {
        key: "logout",
        className: styles.logout,
        label: "退出登录",
        onClick() {
          userStore.logout().then(() => {
            setOpen(false);
            window.location.href = "/";
            clearAccount();
          });
        },
      },
    ];
  }, [onlineUnReadCount, openMeiDouRecordsPopup]);

  if (!isLogin || !user) {
    return (
      <Tooltip title="点击登录">
        <span
          className={styles.accountAvatar}
          onClick={() => {
            wheeLoginPopup({});
          }}
        >
          <Avatar icon={<UserBoldOutlined />} />
        </span>
      </Tooltip>
    );
  }

  return (
    <Dropdown
      overlayClassName={styles.dropdownOverlay}
      menu={{ items: menuItems }}
      trigger={["hover", "click"]}
      open={open}
      onOpenChange={setOpen}
      dropdownRender={(menu) => (
        <>
          {user && (
            <div className={styles.account}>
              {user.avatar && (
                <Avatar
                  src={toAtlasImageView2URL(user.avatar, {
                    mode: 2,
                    width: 84,
                  })}
                  icon={<UserBoldOutlined />}
                  size="large"
                />
              )}
              <span className={styles.accountProfile}>
                <strong>{user.screenName}</strong>
                <small>UID: {user.id}</small>
              </span>
            </div>
          )}

          {menu}
        </>
      )}
    >
      <span className={styles.accountAvatar}>
        <Badge dot color="#F74640" count={onlineUnReadCount}>
          {user.avatar && (
            <Avatar
              src={toAtlasImageView2URL(user.avatar, {
                mode: 2,
                width: 60,
              })}
              icon={<UserBoldOutlined />}
            />
          )}
        </Badge>
      </span>
    </Dropdown>
  );
}

export default observer(AccountAvatar);
