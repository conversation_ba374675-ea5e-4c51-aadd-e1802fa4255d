export const MemberLogo = () => (
  <svg
    width="43"
    height="11"
    viewBox="0 0 43 11"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.30252 0.132812C8.43637 0.132812 8.55403 0.221491 8.59091 0.350167L10.0444 5.42222L11.2689 0.362251C11.3015 0.227631 11.422 0.132812 11.5605 0.132812H14.1305C14.3308 0.132812 14.4749 0.325374 14.4183 0.517534L11.6511 9.91753C11.6135 10.0452 11.4963 10.1328 11.3633 10.1328H9.07483C8.94318 10.1328 8.82691 10.047 8.78813 9.92118L7.26206 4.96995L5.73599 9.92118C5.69722 10.047 5.58095 10.1328 5.4493 10.1328H3.16078C3.02764 10.1328 2.91044 10.0451 2.87293 9.91733L0.112902 0.517331C0.0564934 0.325216 0.200526 0.132812 0.400751 0.132812H2.97077C3.10939 0.132812 3.22996 0.227782 3.26243 0.362543L4.48067 5.41886L5.93322 0.350167C5.9701 0.221491 6.08776 0.132812 6.22161 0.132812H8.30252Z"
      fill="url(#paint0_linear_5825_172986)"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M22.0713 9.83281C22.0713 9.9985 22.2057 10.1328 22.3713 10.1328H24.8507C25.0164 10.1328 25.1507 9.9985 25.1507 9.83281V0.432813C25.1507 0.267127 25.0164 0.132812 24.8507 0.132812H22.3713C22.2057 0.132812 22.0713 0.267127 22.0713 0.432813V3.84277H18.4658V0.432813C18.4658 0.267127 18.3315 0.132812 18.1658 0.132812H15.6864C15.5207 0.132812 15.3864 0.267127 15.3864 0.432813V9.83281C15.3864 9.9985 15.5207 10.1328 15.6864 10.1328H18.1658C18.3315 10.1328 18.4658 9.9985 18.4658 9.83281V6.28358H22.0713V9.83281Z"
      fill="url(#paint1_linear_5825_172986)"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M33.6423 10.1328C33.808 10.1328 33.9423 9.9985 33.9423 9.83281V7.98568C33.9423 7.81999 33.808 7.68568 33.6423 7.68568H29.35V6.24559H33.3825C33.5482 6.24559 33.6825 6.11127 33.6825 5.94559V4.21241C33.6825 4.04672 33.5482 3.91241 33.3825 3.91241H29.35V2.57362H33.6423C33.808 2.57362 33.9423 2.4393 33.9423 2.27362V0.432813C33.9423 0.267127 33.808 0.132812 33.6423 0.132812H26.5706C26.4049 0.132812 26.2706 0.267127 26.2706 0.432813V9.83281C26.2706 9.9985 26.4049 10.1328 26.5706 10.1328H33.6423Z"
      fill="url(#paint2_linear_5825_172986)"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M42.3705 10.1328C42.5361 10.1328 42.6705 9.9985 42.6705 9.83281V7.98568C42.6705 7.81999 42.5361 7.68568 42.3705 7.68568H38.0781V6.24559H42.1106C42.2763 6.24559 42.4106 6.11127 42.4106 5.94559V4.21241C42.4106 4.04672 42.2763 3.91241 42.1106 3.91241H38.0781V2.57362H42.3705C42.5361 2.57362 42.6705 2.4393 42.6705 2.27362V0.432813C42.6705 0.267127 42.5361 0.132812 42.3705 0.132812H35.2987C35.133 0.132812 34.9987 0.267127 34.9987 0.432813V9.83281C34.9987 9.9985 35.133 10.1328 35.2987 10.1328H42.3705Z"
      fill="url(#paint3_linear_5825_172986)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_5825_172986"
        x1="2"
        y1="2.5"
        x2="41.5"
        y2="6.49999"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#3C2404" />
        <stop offset="1" stopColor="#5E3C00" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_5825_172986"
        x1="2"
        y1="2.5"
        x2="41.5"
        y2="6.49999"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#3C2404" />
        <stop offset="1" stopColor="#5E3C00" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_5825_172986"
        x1="2"
        y1="2.5"
        x2="41.5"
        y2="6.49999"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#3C2404" />
        <stop offset="1" stopColor="#5E3C00" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_5825_172986"
        x1="2"
        y1="2.5"
        x2="41.5"
        y2="6.49999"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#3C2404" />
        <stop offset="1" stopColor="#5E3C00" />
      </linearGradient>
    </defs>
  </svg>
);

export const VipLogo = () => (
  <svg
    width="19"
    height="11"
    viewBox="0 0 19 11"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_5825_172987)">
      <rect
        x="0.17041"
        y="0.132812"
        width="18"
        height="10"
        rx="2"
        fill="url(#paint0_linear_5825_172987)"
      />
      <path
        opacity="0.25"
        d="M5.83628 10.1328L13.6382 0.132812H18.1705V10.1328H5.83628Z"
        fill="url(#paint1_linear_5825_172987)"
      />
      <g filter="url(#filter0_d_5825_172987)">
        <path
          d="M4.26946 8.54297C4.3615 8.54297 4.46004 8.48663 4.51426 8.40301L8.67032 1.99348C8.75662 1.86039 8.69432 1.72266 8.54782 1.72266H7.11336C7.0181 1.72266 6.91634 1.78292 6.86358 1.87056L4.08365 6.48916L4.05394 1.87056C4.05338 1.78292 3.9875 1.72266 3.89224 1.72266H2.41539C2.2689 1.72266 2.12458 1.86039 2.13163 1.99348L2.47103 8.40301C2.47546 8.48663 2.54045 8.54297 2.6325 8.54297H4.26946Z"
          fill="white"
        />
        <path
          d="M8.65708 8.54297C8.77544 8.54297 8.89876 8.45101 8.93254 8.33758L10.8409 1.92805C10.8746 1.81461 10.8061 1.72266 10.6877 1.72266H9.37282C9.25447 1.72266 9.13114 1.81461 9.09737 1.92805L7.18904 8.33758C7.15527 8.45101 7.22383 8.54297 7.34219 8.54297H8.65708Z"
          fill="white"
        />
        <path
          d="M11.925 1.72266C11.8066 1.72266 11.6833 1.81461 11.6495 1.92805L11.2962 3.11466H13.0397L13.0413 3.10934H13.8459C14.226 3.10934 14.4745 3.20603 14.6074 3.36805C14.7392 3.52859 14.7732 3.77246 14.6725 4.11062C14.5712 4.45086 14.3906 4.69584 14.1619 4.85702C13.9311 5.01972 13.6237 5.11642 13.2436 5.11642H10.7002L9.74119 8.33758C9.70741 8.45101 9.77598 8.54297 9.89434 8.54297H11.2092C11.3276 8.54297 11.4509 8.45101 11.4847 8.33758L12.0349 6.48956H13.1455C13.8965 6.48956 14.6143 6.25472 15.2011 5.83326C15.7889 5.41115 16.2307 4.81214 16.4409 4.10611C16.6513 3.39956 16.5755 2.80092 16.2525 2.37884C15.9297 1.95706 15.3686 1.72266 14.6354 1.72266H11.925Z"
          fill="white"
        />
      </g>
    </g>
    <defs>
      <filter
        id="filter0_d_5825_172987"
        x="1.13135"
        y="0.722656"
        width="16.4275"
        height="8.82031"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset />
        <feGaussianBlur stdDeviation="0.5" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_5825_172987"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_5825_172987"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_5825_172987"
        x1="1.01409"
        y1="2.5"
        x2="17.8169"
        y2="3.21777"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#5D3C01" />
        <stop offset="1" stopColor="#744B01" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_5825_172987"
        x1="13.1452"
        y1="-0.112583"
        x2="13.1452"
        y2="10.1953"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="white" />
        <stop offset="1" stopColor="white" stopOpacity="0" />
      </linearGradient>
      <clipPath id="clip0_5825_172987">
        <rect
          x="0.17041"
          y="0.132812"
          width="18"
          height="10"
          rx="2"
          fill="white"
        />
      </clipPath>
    </defs>
  </svg>
);
