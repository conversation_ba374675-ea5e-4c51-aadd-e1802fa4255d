.back {
  display: flex;
  box-sizing: border-box;
  height: 32px;
  padding: 0px 10px;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-6, 6px);
  background-color: #fff;
  border: 1px solid #E2E8F0;
  color: #293545;

  &:hover {
    background: #fff !important;
    color: #3042e5 !important;
    border-color: #3042e5 !important;
  }

  :global {
    .icon {
      margin-right: 4px;

      svg {
        width: 16px;
        height: 16px;
      }
    }

    .label {
      font-size: 14px;
    }
  }
}