.account-avatar {
  display: inline-flex;
  align-items: center;
  padding: 8px 0;
  border-radius: 8px;
  line-height: 1;
  transition: background-color 0.2s;
  cursor: pointer;
  user-select: none;
  position: relative;
  margin-right: 10px;


  &.open,
  &:hover {
    // background: #f5f5f5;
  }

  :global .ant-avatar {
    // border-color: #f5f5f5;
    // background: #f5f5f5;
    // color: #f5f5f5;
    .iconfont {
      font-size: 24px;
    }

    + strong {
      margin-left: 8px;
    }
  }

  .vip-icon {
    width: 10px;
    height: 10px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 4px;
    border-radius: var(--radius-capsule, 999px);
    border: 1px solid #0C0E0F;
    background: var(--system-content-brandPrimary, #3549FF);
    position: absolute;
    bottom:8px;
    right: 0;
    svg {
      width: 6px;
      height: 5px;
    }
    &-2 {
      background: linear-gradient(127deg, #53F6B4 9.33%, #CDDBFF 47.5%, #FFC0EA 85.67%);
    }
    
  }
}

.dropdown-overlay {
  width: 228px;

  border-radius: var(--radius-12, 12px);
  border: 1px solid var(--system-stroke-input-default, #fff);
  background: var(--system-background-secondary, #fff);
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 24px 128px 0px rgba(0, 0, 0, 0.16);
  // backdrop-filter: blur(14px);
  &:global(.ant-dropdown) :global .ant-dropdown-menu {
    box-shadow: none;

    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu {
      &:local(.logout) {
        padding-top: 8px;
        padding-bottom: 8px;
        text-align: center;

        :global(.ant-dropdown-menu-submenu-expand-icon) {
          display: none;
        }
      }
    }

    .ant-dropdown-menu-submenu-title {
      padding-right: 16px;
    }
  }

  ul {
    box-shadow: none !important;
  }

  .account {
    display: flex;
    padding: 16px 16px 8px;
    cursor: default;

    &:hover {
      background: transparent;
    }



    .vip-icon {
      width: 10px;
      height: 10px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-left: 4px;
      border-radius: var(--radius-capsule, 999px);
      border: 1px solid #0C0E0F;
      background: var(--system-content-brandPrimary, #3549FF);
      position: absolute;
      bottom:2px;
      right: 0;
      svg {
        width: 6px;
        height: 5px;
      }
      &-2 {
        background: linear-gradient(127deg, #53F6B4 9.33%, #CDDBFF 47.5%, #FFC0EA 85.67%);
      }
      
    }

    :global .ant-avatar {
      + :local(.account-profile) {
        margin-left: 16px;
      }

      &-lg {
        width: 44px;
        height: 44px;
      }
    }

    &-profile {
      display: flex;
      flex: {
        direction: column;
        auto: auto;
      }

      strong {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 500;
        line-height: 24px;
        font-size: 16px;
        color: #1c1d1f;
        text-overflow: ellipsis;
        /* text_14 */
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
      }

      small {
        font-size: 12px;
        line-height: 16px;
        color: #999;
        color: var(--system-content-thirdary, #6B7A8F);
      text-overflow: ellipsis;
      /* text_12 */
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      }
    }
  }
  .account-avatar-wrapper {
    position: relative;
    margin-right: 10px;
  }

  .feedback {
    :global .ant-dropdown-menu-title-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 16px;
      height: 30px;
      font-weight: 600;
    }
  }

  .member-desc {
    // background-image: url('~@/assets/images/container.jpg');
    padding: 12px 8px !important;
    margin: 0 8px !important;
    border-radius: 8px !important;
    background-size: cover;
    color: #1c1d1f;

    .member-card {
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-between;
      align-items: center;

      .left {
        display: flex;
        flex-direction: column;
        gap: 6px;

        .logo {
          display: flex;
          gap: 3px;
        }

        .text {
          color: #3c2404;
          font-size: 12px;
          line-height: 16px;
        }
      }

      .right {
        padding: 0px 12px !important;
        height: 22px !important;
        display: flex;
        align-items: center;

        color: var(--content-on-vip-btn, #3c2404) !important;
        font-size: 12px !important;
        line-height: 16px;

        border: none;
        border-radius: 8px !important;
        background: var(
          --background-whee-vip-btn,
          linear-gradient(270deg, #ffd281 0%, #ffebbc 100%)
        ) !important;
      }
    }
  }

  :global .ant-badge {
    line-height: inherit !important;
  }
}

.ant-dropdown-menu-item-divider {
  background-color: rgba(5, 5, 5, 0.06) !important;
}
