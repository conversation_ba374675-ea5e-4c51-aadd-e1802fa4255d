.bean {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  border-radius: 8px;
  font-size: 14px;
  border: 1px solid #E2E8F0;
  margin-right: 10px;
  background-color: #fff;
  color: #293545;

  &:hover {
    background-color: #E2E8F0 !important;
    color: #293545 !important;
    border: 1px solid #E2E8F0 !important;
  }

  :global {
    .mt-bean {
      cursor: pointer;
    }

    .mt-bean-icon {
      margin-right: 4px;

      svg {
        width: 16px;
        height: 16px;
        color: rgba(251, 205, 111, 1);
      }
    }
  }
}

:global(.popoverOverlay) {
  :global(.ant-popover-content) {
    max-width: 288px;
  }

  :global(.ant-popover-inner) {
    padding: 20px;
    color: #939599;
    font-size: 12px !important;
    font-weight: 400;
    line-height: 16px;

    .contents {
      font-size: 12px !important;
      white-space: pre-wrap;
      margin-bottom: 16px;
    }

    .benefitsDesc {
      font-size: 12px;
      color: #1c1d1f;
      margin-bottom: 8px;
    }
  }
}

.title {
  color: #1c1d1f;
  font-size: 18px;
  font-weight: 700;
  line-height: 20px;

  .container {
    position: relative;
  }

  .infoCircle {
    cursor: pointer;
    color: #abadb2;
  }
}