"use client";

import { MtBeanFilled } from "@meitu/candy-icons";
import classNames from "classnames";
import { useMeiDouBalance } from "@/hooks/useMeidou";
import styles from "./index.module.scss";
import buttonStyles from "@/styles/button.module.scss";
import { observer } from "mobx-react-lite";
import { useStore } from "@/contexts/StoreContext";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import { MemberGroupCategory, VipLevel } from "@/types";

import { SubscribeModalType } from "@/components/SubscribeModal/types";
function MtBean({ onClick }: { onClick?: () => void }) {
  const { userStore } = useStore();
  const vipLevel = userStore?.vipLevel;
  // const vipLevel = VipLevel.None;
  const { availableAmount } = useMeiDouBalance({ userStore });
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();

  if (!userStore.isReady) {
    return;
  }

  const handleClick = () => {
    onClick?.();
    openMeiDouRecordsPopup(MemberGroupCategory.Meidou);
    // // 如果是高级会员，点击打开充值美豆页面
    // if(vipLevel === VipLevel.Plus) {
    //   openMeiDouRecordsPopup();
    // } else {
    //   // 如果是普通会员，点击打订阅会员
    //   open({
    //     productType: SubscribeModalType.Basic,
    //     onSuccess: () => {
    //       close();
    //     }
    //   })
    // }
  };

  return (
    <div
      className={classNames(styles.bean, buttonStyles.secondary, "mt-bean")}
      onClick={handleClick}
    >
      <MtBeanFilled className="mt-bean-icon" />
      {availableAmount}
    </div>
  );
}

export default observer(MtBean);
