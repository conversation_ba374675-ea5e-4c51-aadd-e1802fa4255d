"use client";

import { Info<PERSON>ircleBold, MtBeanFilled } from "@meitu/candy-icons";
import classNames from "classnames";
import { useMeiDouBalance } from "@/hooks/useMeidou";
import styles from "./index.module.scss";
import buttonStyles from "@/styles/button.module.scss";
import { observer } from "mobx-react-lite";
import { useStore } from "@/contexts/StoreContext";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import { MemberGroupCategory, VipLevel } from "@/types";
import { Col, Popover, Row, Space } from "antd";
import { useState, useRef } from "react";
import { Button } from "@/components/Button";
import { VipButton } from "../NewAccountAvatar/VipButton";

// Detail 组件
const Detail = ({ detailTitle, detailDesc, benefitsDetail }: any) => (
  <div>
    <h4>{detailTitle}</h4>
    <p>{detailDesc}</p>
    {benefitsDetail?.map((item: any, index: number) => (
      <div key={index}>
        <span>{item.desc}</span>
        <span>{item.value}</span>
      </div>
    ))}
  </div>
);

function MtBean({ onClick }: { onClick?: () => void }) {
  const { userStore } = useStore();
  const {
    availableAmount,
    tips,
    benefitsDescription,
    detailTitle,
    detailDesc,
    benefitsDetail,
  } = useMeiDouBalance({ userStore });
  const { isVipCurrent } = userStore;
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();

  const [popverOpen, setPopverOpen] = useState(false);
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const containerRef = useRef<HTMLSpanElement>(null);

  if (!userStore.isReady) {
    return;
  }

  return (
    <Popover
      overlayClassName={styles.popoverOverlay}
      placement="bottomLeft"
      arrow={false}
      trigger={"hover"}
      open={popverOpen}
      onOpenChange={(open) => {
        setPopverOpen(open);
        if (!open) {
          setDetailModalOpen(false);
          return;
        }
      }}
      content={
        <>
          {benefitsDescription && (
            <p className={styles.benefitsDesc}>{benefitsDescription}</p>
          )}
          <pre className={styles.contents}>{tips} </pre>
          <Row gutter={12}>
            <Col flex={1}>
              <Button
                type="default"
                onClick={() => {
                  openMeiDouRecordsPopup(MemberGroupCategory.Meidou);
                  setPopverOpen(false);
                }}
                block
              >
                购买美豆
              </Button>
            </Col>
            <Col flex={1}>
              <VipButton
                onClick={() => {
                  openMeiDouRecordsPopup(MemberGroupCategory.Member);
                  setPopverOpen(false);
                }}
                block
              >
                {isVipCurrent ? "续费会员" : "开通会员"}
              </VipButton>
            </Col>
          </Row>
        </>
      }
      title={
        <Space size={4} className={styles.title}>
          <span ref={containerRef} className={styles.container}>
            <MtBeanFilled />
          </span>
          {availableAmount}
          <Popover
            overlayClassName={styles.detail}
            arrow={false}
            open={detailModalOpen}
            content={
              <Detail
                detailTitle={detailTitle}
                detailDesc={detailDesc}
                benefitsDetail={benefitsDetail}
              />
            }
            getPopupContainer={(trigger) => containerRef.current ?? trigger}
          >
            <InfoCircleBold
              className={styles.infoCircle}
              onClick={() =>
                setDetailModalOpen((preDetailModalOpen) => !preDetailModalOpen)
              }
            />
          </Popover>
        </Space>
      }
    >
      <div
        className={classNames(styles.bean, buttonStyles.secondary, "mt-bean")}
      >
        <MtBeanFilled className="mt-bean-icon" />
        {availableAmount}
      </div>
    </Popover>
  );
}

export default observer(MtBean);
