/**
 * 基于antd的button组件，添加了一些样式
 */

import { Button as AntdButton, ConfigProvider } from "antd";
import { ButtonProps } from "antd";
import "./index.style.scss";
export const Button = (props: ButtonProps) => {
  return (
    <ConfigProvider theme={{
        components: {
            Button: {
                colorPrimary: '#3549FF',
                primaryShadow: '0px 0px 14.7px 1px rgba(255, 255, 255, 0.00) inset;',
                primaryColor: '#fff',
                colorPrimaryHover: 'rgba(53, 73, 255, 0.9)',
                // 主态悬浮border
                // defaultHoverBorderColor: 'rgba(255, 255, 255, 0.20)',
                colorPrimaryActive: '#181818',
                colorBgContainerDisabled: 'rgba(20, 31, 51, 0.12)', // 禁用状态的背景颜色
                colorTextDisabled: '#4A5564', // 禁用状态的文字颜色
               
            },
        },
    }}>
      <AntdButton
        {...props}
        type={props.type ?? "primary"}
        className={`${props.className} button-primary`}
      />
    </ConfigProvider>
  );
};
