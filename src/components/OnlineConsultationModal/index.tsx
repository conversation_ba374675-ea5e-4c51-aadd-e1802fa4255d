"use client";
import { CrossBoldOutlined } from "@meitu/candy-icons";
import { Modal } from "antd";
import { useState } from "react";
import styles from "./index.module.scss";
import qs from "qs";
import {
  setM<PERSON>enger<PERSON><PERSON><PERSON>,
  bind<PERSON><PERSON><PERSON>,
  businessMessenger,
} from "@meitu/feedback-web-bridge/es/business";
import type { SetNavigationBarTitleParams } from "@meitu/feedback-web-bridge/es/business";
import mtstat from "@meitu/mtstat-sdk";
import { getConfig } from "@/config";
import { getAccountAccessToken } from "@/utils/account";
import { observer } from "mobx-react";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { isBeta, isDev, isPre } from "@/constants/environment";

const config = getConfig();

// 根据环境获取API地址
function getBaseUrl(): string {
  if (isBeta) return "https://beta-feedback.meitu.com/im";
  if (isDev || isPre) return "https://pre-feedback.meitu.com/im";
  return "https://pre-feedback.meitu.com/im";
}

/**
 * 在线咨询对话框
 */
const OnlineConsultationModal = () => {
  const { editorStatusStore } = useRootStore();

  const [modalTitle, setModalTitle] = useState("在线咨询");

  const search = qs.stringify(
    {
      client_id: config.ACCOUNT_CLIENT_ID,
    },
    { addQueryPrefix: true }
  );
  const iframeSrc = `${getBaseUrl()}${search}`;

  businessMessenger.setTarget(iframeSrc);

  bindHandlers({
    getAppInfo() {
      return {
        gid: mtstat.getDeviceId(),
        language: "zh_CN",
      };
    },
    // 跨域时必须提供该方法
    getMeituAccountEncryptedToken() {
      return {
        encryptedToken: getAccountAccessToken(),
      };
    },
    getUserInfo() {
      return {};
    },
    setNavigationBarTitle({ title }: SetNavigationBarTitleParams) {
      if (typeof title === "string") {
        setModalTitle(title);
      } else {
        // eslint-disable-next-line no-throw-literal
        throw "title 参数错误";
      }
    },
  });

  return (
    <Modal
      className={styles.consultationModal}
      rootClassName={styles.root}
      title={modalTitle}
      open={editorStatusStore.showConsultationModal}
      footer={null}
      maskClosable={false}
      closeIcon={<CrossBoldOutlined />}
      destroyOnClose
      width={368}
      mask={false}
      onCancel={() => {
        editorStatusStore.setShowConsultationModal(false);
      }}
      getContainer={"#root"}
    >
      <iframe
        title="在线咨询"
        src={setMessengerOrigin(iframeSrc)}
        frameBorder={0}
      />
    </Modal>
  );
};

export default observer(OnlineConsultationModal);
