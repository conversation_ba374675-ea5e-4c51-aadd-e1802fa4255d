$header-height: 48px;

$system-content-brand-primary: var(--system-content-brandPrimary, #3549FF);
$system-content-brand-secondary: var(--system-content-brandSecondary, #3549FF);
$system-content-brand-thirdary: var(--system-content-brandThirdary, #293545);
$system-content-on-primary: var(--system-content-onPrimary, #181818);
$system-content-on-secondary: var(--system-content-onSecondary, #fff);
$system-content-primary: var(--system-content-primary, #fff);
$system-content-secondary: var(--system-content-secondary, #293545);
$system-header-secondary: var(--system-header-secondary, #293545);
$system-header-thirdary: var(--system-header-thirdary, #F5F7FA);
$system-content-thirdary: var(--system-content-thirdary, #6A7B94);
$system-content-tertiary: $system-content-secondary;
$system-content-fourth: var(--system-content-forth, #C9D1DB);

$system-background-primary: var(--system-background-primary, #FFFFFF);
$system-background-secondary: var(--system-background-secondary, #F1F5F9);
$system-background-thirdary: var(--system-background-thirdary, #272C33);
$system-background-fourth: var(--system-background-fourth, #303640);
$system-background-fifth: var(--system-background-fifth, #F1F5F9);
$system-background-sixth: var(--system-background-sixth, #fff);
$system-background-seventh: var(--system-background-seventh, #F5F7FA);
$system-background-input: var(--system-background-input, #16171C);

// $system-stroke-input: var(--system-stroke-input, rgba(34, 39, 46, 1));
$system-stroke-input: var(--system-stroke-input-secondary, #E2E8F0);
$system-stroke-input-default: var(--system-stroke-input-default, #22272E);
$system-stroke-button: var(--system-stroke-button, #323B48);
$system-stroke-divider: var(--system-stroke-divider, #22272E);

// $background-editor-popup-default: var(--background-editorPopup-default, #1D1E23);
$background-editor-popup-default: var(--background-editorPopup-default, #fff);
$background-editor-popup-hover: var(--background-editorPopup-hover, #F5F7FA);
$background-system-frame-float-panel: var(--background-system-frame-float-panel, #FFFFFF);
$background-input: var(--background-input, #FFFFFF);

$stroke-editor-border-overlay: var(--stroke-editorBorderOverlay, rgba(255, 255, 255, 0.08));
$tab-font-color: var(--tab-font-color, #1C1D1F);
