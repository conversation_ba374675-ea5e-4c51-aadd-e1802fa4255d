.meitu-subscribe[data-theme='light'] {
  /* Color styles */
  --content-meidou-cornermark: rgba(87, 51, 27, 1);
  --background-qrcode-failure: rgba(255, 255, 255, 0.8);
  --content-button-tertiary: rgba(185, 188, 196, 1);
  --background-scrollbar-pressed: rgba(240, 242, 245, 1);
  --content-button-ghost: rgba(49, 52, 56, 1);
  --content-button-outline: rgba(99, 80, 54, 1);
  --background-frame: rgba(247, 248, 250, 1);
  --content-warning: rgba(242, 78, 78, 1);
  --content-vip-cornermark: rgba(0, 21, 51, 1);
  --content-switch-button: rgba(255, 255, 255, 1);
  --background-switch-button: rgba(0, 0, 0, 0.4);
  --background-scrollbar: rgba(247, 248, 250, 1);
  --background-mask-overlay: rgba(0, 0, 0, 0.5);
  --content-meidou-button: rgba(87, 51, 27, 1);
  --background-form: rgba(247, 248, 250, 1);
  --content-close: rgba(0, 0, 0, 0.8);
  --stroke-meidou: rgba(250, 181, 135, 1);
  --content-link: rgba(53, 73, 255, 1);
  --content-corner-badge: rgba(255, 255, 255, 1);
  --background-meidou-button: rgba(250, 181, 135, 1);
  --background-button-secondary: rgba(255, 255, 255, 0.05);
  --content-price-package-selected: rgba(99, 80, 54, 1);
  --background-price-package-selected: rgba(255, 250, 240, 1);
  --content-carousel: rgba(52, 54, 56, 0.15);
  --background-corner-badge-secondry: rgba(250, 181, 135, 1);
  --content-carousel-unselected: rgba(255, 255, 255, 0.5);
  --stroke-border-overlay: rgba(0, 0, 0, 0.1);
  --background-tab-selected: rgba(255, 255, 255, 1);
  --content-tab: rgba(0, 0, 0, 0.6);
  --background-price-package: rgba(255, 255, 255, 1);
  --background-button-hover: rgba(52, 54, 56, 0.05);
  --content-tab-selected: rgba(0, 0, 0, 1);
  --content-carousel-selected: rgba(255, 255, 255, 1);
  --content-price: rgba(99, 80, 54, 1);
  --stroke-button-main: rgba(99, 80, 54, 1);
  --background-tab: rgba(247, 248, 250, 1);
  --content-separtor: rgba(52, 54, 56, 0.05);
  --background-secondary: rgba(247, 248, 250, 1);
  --stroke-banner: rgba(0, 0, 0, 0.1);
  --background-price-hover: rgba(240, 242, 245, 1);
  --background-button-disable: rgba(240, 242, 245, 1);
  --background-vip-cornermarkgrey: rgba(185, 188, 196, 1);
  --stroke-carousel: rgba(52, 54, 56, 0.2);
  --background-primary: rgba(255, 255, 255, 1);
  --content-button-secondary: rgba(92, 95, 102, 1);
  --background-toast: rgba(0, 0, 0, 0.6);
  --content-button-disable: rgba(185, 188, 196, 1);
  --content-vip-cornermarkgrey: rgba(255, 255, 255, 1);
  --background-tertiary: rgba(240, 242, 245, 1);
  --stroke-button-secondry: rgba(218, 221, 227, 1);
  --stroke-price-package: rgba(240, 242, 245, 1);
  --content-quarternary: rgba(52, 54, 56, 0.2);
  --background-corner-badge: rgba(235, 157, 106, 1);
  --content-primary: rgba(0, 0, 0, 1);
  --background-meidou-selected: rgba(255, 239, 221, 1);
  --content-meidou: rgba(250, 181, 135, 1);
  --content-tertiary: rgba(0, 0, 0, 0.3);
  --content-secondary: rgba(0, 0, 0, 0.6);
  --background-switch-mask: linear-gradient(90deg,
      #ffffff00,
      #ffffff78,
      #ffffffff);
  --background-vip-cornermark: linear-gradient(270deg, #ffd281ff, #ffebbcff);
  --background-price-cornermark: linear-gradient(90deg, #ff5454ff, #ff7676ff);
  --background-price-cornermark-secondary: linear-gradient(90deg,
      #ff5454ff,
      #ff7676ff);
  --background-price-package-count-down: linear-gradient(180deg,
      #ffd88eff,
      #ffe5aeff);
  --background-button-main: linear-gradient(270deg, #ffd281 0%, #ffebbc 100%);

  --content-price-cornermark-text: rgba(255, 255, 255, 1);
  --content-price-cornermark-text-secondary: rgba(255, 255, 255, 1);
  --content-price-package-count-down: rgba(99, 80, 54, 1);
  --content-vip-cornermark: rgba(60, 36, 4, 1);
  --content-button-main: rgba(34, 35, 38, 1);
  --stroke-price-package-selected: #ffd88eff;
  /* Radius styles */
  --test-radius: 4px 4px 0px 0px;
  --radius-capsule: 999px;
  --radius-64: 64px;
  --radius-56: 56px;
  --radius-24: 24px;
  --radius-20: 20px;
  --radius-48: 48px;
  --radius-16: 16px;
  --radius-40: 40px;
  --radius-12: 12px;
  --radius-32: 32px;
  --radius-8: 8px;
  --radius-4: 4px;
  --radius-2: 2px;
  --radius-0: 0px;
  /* Spacing styles */
  --spacing-64: 64px;
  --spacing-60: 60px;
  --spacing-56: 56px;
  --spacing-52: 52px;
  --spacing-48: 48px;
  --spacing-44: 44px;
  --spacing-40: 40px;
  --spacing-32: 32px;
  --spacing-28: 28px;
  --spacing-24: 24px;
  --spacing-12: 12px;
  --spacing-8: 8px;
  --spacing-4: 4px;
  --spacing-20: 20px;
  --spacing-2: 2px;
  --spacing-36: 36px;
  --spacing-16: 16px;
  --spacing-0: 0px;
  // 美豆订阅样式
  // 权益图标
  --right-rule-gift-icon: url('../assets/images/gift-icon.png');

  // 订阅弹窗背景
  --result-modal-background: url('../assets/images/success-bg.png'); // 支付弹窗美豆背景

  // 支付弹窗
  --result-modal-meidou-background: url('../assets/images/success-meidou-light.png'); // 支付弹窗美豆背景

  // 美豆（代币）图标
  --meidou-icon: url('../assets/images/mei-dou.png');
}