import { isBeta, isDev, isPre } from "@/constants/environment";
import { getAccountAccessToken } from "@/utils/account";
import { shimRequestGeneralParams } from "@/utils/request/request";
import { createPCBAxiosInstance } from "@meitu/util";
import type { CreateAxiosDefaults } from "axios";

// 根据环境获取API地址
function getApiUrl(): string {
  if (isBeta) return "https://betaapi-feedback.meitu.com";
  if (isDev || isPre) return "https://preapi-feedback.meitu.com";
  return "https://api-feedback.meitu.com";
}

export function createFeedbackInstance(config?: CreateAxiosDefaults) {
  return createPCBAxiosInstance(
    {
      ...config,
      baseURL: getApiUrl() + (config?.baseURL || ""),
    },
    {
      beforeRequest(config) {
        config.headers["Access-Token"] = getAccountAccessToken();
        return shimRequestGeneralParams(config);
      },
    }
  );
}
