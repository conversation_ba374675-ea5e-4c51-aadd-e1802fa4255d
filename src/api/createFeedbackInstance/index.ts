import { isBeta, isDev, isPre, isRelease } from "@/constants/environment";
import { getAccountAccessToken } from "@/utils/account";
import { shimRequestGeneralParams } from "@/utils/request/request";
import { createPCBAxiosInstance } from "@meitu/util";
import type { CreateAxiosDefaults, AxiosInstance } from "axios";

/**
 * API endpoint mapping for different environments
 */
const API_ENDPOINTS = {
  dev: "https://preapi-feedback.meitu.com",
  pre: "https://preapi-feedback.meitu.com",
  beta: "https://betaapi-feedback.meitu.com",
  release: "https://api-feedback.meitu.com",
} as const;

/**
 * Get the appropriate API endpoint based on current environment
 */
function getApiEndpoint(): string {
  if (isDev || isPre) return API_ENDPOINTS.dev;
  if (isBeta) return API_ENDPOINTS.beta;
  if (isRelease) return API_ENDPOINTS.release;

  // Fallback to production API if environment is not recognized
  console.warn("Unknown environment, falling back to production API");
  return API_ENDPOINTS.release;
}

// Cache for instances to avoid recreating identical instances
const instanceCache = new Map<string, AxiosInstance>();

/**
 * Generate a cache key based on configuration
 */
function getCacheKey(config?: CreateAxiosDefaults): string {
  const baseURL = config?.baseURL || "";
  const timeout = config?.timeout || 0;
  return `${baseURL}-${timeout}`;
}

/**
 * Creates a feedback API instance with proper authentication and request handling
 * @param config - Optional axios configuration
 * @returns Configured axios instance for feedback API
 */
export function createFeedbackInstance(
  config?: CreateAxiosDefaults
): AxiosInstance {
  const cacheKey = getCacheKey(config);

  // Return cached instance if available
  if (instanceCache.has(cacheKey)) {
    return instanceCache.get(cacheKey)!;
  }

  const apiEndpoint = getApiEndpoint();
  const baseURL = `${apiEndpoint}${config?.baseURL || ""}`;

  const instance = createPCBAxiosInstance(
    {
      timeout: 10000, // Default 10s timeout
      ...config,
      baseURL,
    },
    {
      beforeRequest(requestConfig) {
        try {
          // Add authentication token
          const token = getAccountAccessToken();
          if (token) {
            requestConfig.headers["Access-Token"] = token;
          }

          // Apply general request parameters
          return shimRequestGeneralParams(requestConfig);
        } catch (error) {
          console.error("Error in feedback instance beforeRequest:", error);
          return requestConfig;
        }
      },
    }
  );

  // Cache the instance
  instanceCache.set(cacheKey, instance);

  return instance;
}

/**
 * Clear the instance cache (useful for testing or when configuration changes)
 */
export function clearFeedbackInstanceCache(): void {
  instanceCache.clear();
}
