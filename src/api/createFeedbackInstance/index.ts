import { isBeta, isDev, isPre, isRelease } from "@/constants/environment";
import { getAccountAccessToken } from "@/utils/account";
import { shimRequestGeneralParams } from "@/utils/request/request";
import { createPCBAxiosInstance } from "@meitu/util";
import type { CreateAxiosDefaults } from "axios";

/**
 * API endpoint mapping for different environments
 */
const API_ENDPOINTS = {
  dev: "https://preapi-feedback.meitu.com",
  beta: "https://betaapi-feedback.meitu.com",
  release: "https://api-feedback.meitu.com",
} as const;

/**
 * Cached API endpoint to avoid repeated environment checks
 */
let cachedApiEndpoint: string | null = null;

/**
 * Get the appropriate API endpoint based on current environment
 * Uses caching to improve performance on repeated calls
 */
function getApiEndpoint(): string {
  if (cachedApiEndpoint) {
    return cachedApiEndpoint;
  }

  // Priority: beta > dev/pre > release (fallback)
  if (isBeta) {
    cachedApiEndpoint = API_ENDPOINTS.beta;
  } else if (isDev || isPre) {
    cachedApiEndpoint = API_ENDPOINTS.dev;
  } else if (isRelease) {
    cachedApiEndpoint = API_ENDPOINTS.release;
  } else {
    // Fallback for unknown environments
    console.warn("Unknown environment detected, defaulting to production API");
    cachedApiEndpoint = API_ENDPOINTS.release;
  }

  return cachedApiEndpoint;
}

export function createFeedbackInstance(config?: CreateAxiosDefaults) {
  const API = getApiEndpoint();
  return createPCBAxiosInstance(
    {
      ...config,
      baseURL: `${API}${config?.baseURL || ""}`,
    },
    {
      beforeRequest(config) {
        try {
          // Add authentication token if available
          const token = getAccountAccessToken();
          if (token) {
            config.headers["Access-Token"] = token;
          }

          // Apply general request parameters
          return shimRequestGeneralParams(config);
        } catch (error) {
          console.error("Error in feedback instance beforeRequest:", error);
          // Return config as-is if there's an error to prevent request failure
          return config;
        }
      },
    }
  );
}
