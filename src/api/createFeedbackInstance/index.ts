import { isBeta, isDev, isPre, isRelease } from "@/constants/environment";
import { getAccountAccessToken } from "@/utils/account";
import { shimRequestGeneralParams } from "@/utils/request/request";
import { createPCBAxiosInstance } from "@meitu/util";
import type { CreateAxiosDefaults } from "axios";

export function createFeedbackInstance(config?: CreateAxiosDefaults) {
  let API = "";
  if (isDev || isPre) {
    API = "https://preapi-feedback.meitu.com";
  } else if (isBeta) {
    API = "https://betaapi-feedback.meitu.com";
  } else if (isRelease) {
    API = "https://api-feedback.meitu.com";
  }
  return createPCBAxiosInstance(
    {
      ...config,
      baseURL: `${API}${config?.baseURL}`,
    },
    {
      beforeRequest(config) {
        config.headers["Access-Token"] = getAccountAccessToken();
        return shimRequestGeneralParams(config);
      },
    }
  );
}
