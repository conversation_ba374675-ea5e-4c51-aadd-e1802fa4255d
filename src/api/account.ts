import { createFeedbackInstance } from "./createFeedbackInstance";

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const imInstance = createFeedbackInstance({ baseURL: "/im_v2" });

export interface UnReadMessageCountParams {
  fid?: number; //意见反馈ID【在意见反馈详情页时需要传递】
  client_id: string;
}
export interface UnReadMessageCountResponse {
  // 在线咨询未读数
  onlineUnreadNum: number;
  // 意见反馈未读数
  feedbackUnreadNum: number;
}

/**
 * 未读消息数量
 */
export function unReadMessageCount(params: UnReadMessageCountParams) {
  return imInstance.get<void, UnReadMessageCountResponse>(
    `/unread_count.json`,
    {
      params,
    }
  );
}
