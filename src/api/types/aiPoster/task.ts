import { ControlNetParams, StyleModel } from "@/types";
import { CommonCreate } from "../common";
import { StyleModelConfigType } from "../editorConfig";

// 画面参考
export interface ControlNetUnitsType {
  /**
   * 是否启用
   */
  enabled: boolean;
  /**
   * 图片地址
   */
  inputImage: string;
  /**
   * 遮罩图（controlnet中间上传图片后，涂抹黑色的部分）
   */
  mask?: string;
  /**
   * 预处理器
   */
  module: string;
  /**
   * 预处理器模型
   */
  model: string;
  /**
   * 模型id
   */
  modelId: number;
  /**
   * 权重
   */
  weight: number;
  /**
   * 引导介入时机（Start） Guidance Start (T)
   */
  guidanceStart: number;
  /**
   * 引导退出时机（End） Guidance End (T)
   */
  guidanceEnd: number;
  /**
   * 画面缩放：1拉伸，2自动缩放，3裁剪
   */
  resizeMode: number;
  /**
   * 预处理参数
   */
  imageProcessParams?: {
    image: string;
    module: string;
    model: string;
  };
}

// 图片表单数据类型
export type ImageParamsForm = {
  baseModelId: number;
  batchSize: number;
  imageChecked: boolean;
  image?: string;
  ratio: string;
  prompt: string;
};
/**
 * 值 0：不存在 alpha 通道，值 1：存在 alpha 通道，值 2：alpha 通道已替换结果图
 */
export enum AlphaStatus {
  NoAlpha = 0,
  HasAlpha = 1,
  ReplacedAlpha = 2,
}

export namespace AIPoster {
  /**
   * 任务类型
   */
  export enum TaskCategory {
    /**
     * 文生图
     */
    Text2Image = "ai_poster_text2img",
    /**
     * 图生图
     */
    Image2Image = "ai_poster_img2img",
    /**
     * ai海报 文生海报
     */
    Poster = "ai_poster",
    /**
     * ai海报  图生海报
     */
    PosterImage = "ai_poster_image",
    /**
     * ai消除
     */
    Eraser = "ai_poster_eraser",
    /**
     * 超清
     */
    Upscaler = "ai_poster_magicsr",
    /**
     * 抠图
     */
    Cutout = "ai_poster_cutout",
    /**
     * 改字
     */
    TextEdit = "ai_poster_textediting",
    /**
     * 改图
     */
    ImageEdit = "ai_poster_inpaint"
  }

  /**
   *  任务类型 对应的functionCode
   */
  export enum TaskFunctionCode {
    /**
     * 文生图
     */
    ai_poster_text2img = "wheeai.texttoimage",
    /**
     * 图生图
     */
    ai_poster_img2img = "wheeai.imagetoimage",
    /**
     * ai海报 文生海报
     */
    ai_poster = "wheeai.posterfromtext",
    /**
     * ai海报  图生海报
     */
    ai_poster_image = "wheeai.posterfromimage",
    /**
     * ai消除
     */
    ai_poster_eraser = "wheeai.inpainting",
    /**
     * 超清
     */
    ai_poster_magicsr = "wheeai.imageupscaling",
    /**
     * 抠图
     */
    ai_poster_cutout = "wheeai.backgroundremoval",
    /**
     * 改字
     */
    ai_poster_textediting = "wheeai.textediting",
    /**
     * 改图
     */
    ai_poster_inpaint = "wheeai.imageediting"
  }

  /**
   * 任务的加载状态
   */
  export enum LoadingStatus {
    /**
     * 任务成功
     */
    Success = "success",
    /**
     * 正在等待任务
     */
    Loading = "loading",
    /**
     * 任务失败
     */
    Failure = "failure",
  }

  /**
   * 图片状态
   */
  export enum ImageStatus {
    /**
     * 正常
     */
    Success = 1,
    /**
     * 审核不通过
     */
    AuditFailed = 2,
  }

  export type ResultImage = {
    /**
     * 带签名的链接
     */
    urlSign: string;
    /**
     * 没有签名的链接
     */
    urlShort: string;
    /**
     * 图片状态
     */
    imageStatus: ImageStatus;
    /**
     * 值 0：不存在 alpha 通道，值 1：存在 alpha 通道，值 2：alpha 通道已替换结果图
     */
    alphaStatus?: AlphaStatus;
    width?: number;
    height?: number;
  };

  export type Task = {
    /**
     * 任务id
     */
    id: string;
    /**
     * 任务类型
     */
    taskCategory: TaskCategory;
    /**
     * 任务状态 在数据请求回来后 会根据该值
     */
    status: number;
    /**
     * 加载状态
     */
    loadingStatus: LoadingStatus;
    /**
     * 任务失败信息
     */
    failMsg?: string;
    /**
     * 原图
     */
    initImage: string[];
    /**
     * 原图水印图
     */

    initWatermarkImage: string;
    /**
     * 创建时间
     */
    createdAt: number;
    /**
     * 结果图
     */
    resultImages: ResultImage[];
    /**
     * 生成参数
     */
    params: Submit.Image2ImageParams;
  };
}

/**
 * 请求的参数类型和响应的数据类型
 */
export namespace Fetch {
  export type ResponseTask = Omit<AIPoster.Task, "loadingStatus">;

  // 获取历史记录列表的请求参数
  export type ListRequest = {
    projectId: number;
  };
  // 获取历史记录列表的响应
  export type ListResponse = {
    list: Array<ResponseTask>;
  };
  // 查询任务的请求参数
  export type QueryRequest = {
    projectId: number;
    ids: string[];
  };
  // 查询任务的响应
  export type QueryResponse = ResponseTask[];

  export type DeleteRecordItem = {
    msgId: string;
    imageUrl: string;
  };
  // 删除记录
  export type DeleteRecordsRequest = {
    projectId: number;
    params: DeleteRecordItem[];
  };
  export type DeleteRecordsResponse = {
    result: boolean;
  };
  export type DownloadImageRequest = {
    projectId: number;
    removeWatermark: boolean;
    params: Array<{
      msgId: string;
      imageUrl: string;
    }>;
  };
  export type DownloadImageResponse = string[];
}

/**
 * 创建任务的请求
 */
export namespace Submit {
  export type Response = {
    id: string;
  };

  export type CutoutParams = {
    initImage: string;
    linkMsgId?: string;
    initWatermarkImage: string;
  };

  export type UpscalerParams = {
    srNum: number;
    imageFile: string;
    alphaStatus: Array<AlphaStatus>;
    linkMsgId?: string;
    initWatermarkImage: string;
  };

  export type EraserParams = {
    initImage: string;
    maskImage: string;
    linkMsgId?: string;
  };

  export type Text2ImageParams = {
    baseModelId: number;
    batchSize: number;
    width: number;
    height: number;
    picRatio: string;
    prompt: string;
  };

  export type Image2ImageParams = Text2ImageParams & {
    initImages?: string[];
    linkMsgId?: string;
    initWatermarkImage: string;
  };

  export type PosterParams = {
    templateId?: number;
    templateCategoryId?: number;
    tgLayoutImageUrl?: string;
    tgEditorParams?: string;
    textGeneration: Array<{
      text: string;
      font: string;
      position: Array<number[]>;
    }>;
  } & FormType.FrameParamsForm;

  export type TextEditParams = {
    batchSize: number;
    initImage: string;
    mediaList: Array<{
      maskImage: string;
      text: string;
    }>;
    linkMsgId?: string;
  };

  export type ImageEditParams = {
    prompt: string,
    batchSize: number,
    initImage: string,
    maskImage: string,
    linkMsgId?: string;
  }

  type TaskParams<T extends AIPoster.TaskCategory> =
    T extends AIPoster.TaskCategory.Cutout
      ? CutoutParams
      : T extends AIPoster.TaskCategory.Text2Image
      ? Text2ImageParams
      : T extends AIPoster.TaskCategory.Image2Image
      ? Image2ImageParams
      : T extends AIPoster.TaskCategory.Poster
      ? PosterParams
      : T extends AIPoster.TaskCategory.PosterImage
      ? PosterParams
      : T extends AIPoster.TaskCategory.Upscaler
      ? UpscalerParams
      : T extends AIPoster.TaskCategory.Eraser
      ? EraserParams
      : T extends AIPoster.TaskCategory.TextEdit
      ? TextEditParams
      : T extends AIPoster.TaskCategory.ImageEdit
      ? ImageEditParams
      : never;

  export type Request<T extends AIPoster.TaskCategory> = {
    projectId: number;
    layerId?: string;
    taskCategory: T;
    params: TaskParams<T>;
  } & CommonCreate;
}

// 画板表单数据类型
export namespace FormType {
  export type FrameParamsForm = {
    batchSize: number;
    width: number;
    height: number;
    picRatio: string;
    prompt: string;
  };
}
