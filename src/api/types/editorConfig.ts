export interface StyleModelConfigType {
  /**
   * 风格模型id
   */
  styleModelId: number;
  /**
   * 风格模型所属分类，多个分类,分隔
   * tips: 主要是埋点使用
   */
  styleModelCategories: string;
  /**
   * 风格模型-权重，数值1-100
   */
  styleModelWeight: number;
  /**
   * 风格模型封面
   */
  styleModelImage?: string;
  /**
   * 风格模型名称
   */
  styleModelName?: string;
  /**
   * 风格模型类型名称
   */
  styleModelTypeName?: string;
}

/**
 * 请求草稿类型
 * img2img图生图
 * txt2img文生图
 */
export enum DraftType {
  /** 图生图 */
  IMAGE_TO_IMAGE = "img2img",
  /** 文生图 */
  TEXT_TO_IMAGE = "txt2img",
  /** 模型训练 */
  MODEL = "model",
  /** 图像扩展 */
  EXTEND = "extend",
  /** AI改图 */
  INPAINT = " inpaint",
  /** 资源位 */
  OPERATE = "operate",
  /** ai模特 */
  AI_MODEL_IMAGE = "aimodelimg",
  /** ai超清 */
  IMAGE_UPSCALE = "magicsr",
  /** ai视频 */
  AI_VIDEO = "ai_video",
  /** Ai消除 */
  AI_ERASER = "ai_eraser",
  /** ip 形象定制概念图设计 */
  IP_CONCEPT = "ipconceptimage",
  /** ip 形象定制 */
  IP_CUSTOMIZATION = "ipfigureimage",
  /** AI免抠素材 素材生成*/
  MATERIAL_GENERATE = "aimaterialgen",
  /** AI免抠素材 素材转换*/
  MATERIAL_TRANSFORM = "aimaterialconv",
  /** ai 海报 */
  AI_POSTER = "ai_poster",
}
export interface EditorConfigQuery {
  /**
   * 模型类型，传参模型类型后，只返回当前类型的模型数据
   * 类型枚举值：1.模型，4.HyperNetwork，6.LoRA，8.Controlnet；
   * 先忽略
   */
  modelType?: MODEL_TYPE;
  /**
   * 模型个数，不传默认1000，先忽略
   */
  count?: number;
  /**
   * 翻页用的标识，先忽略
   */
  cursor?: string;

  // 自定义风格模型 ids 查询
  styleModelIds?: string;
}

/**
 * 返回结果
 */
export interface EditorConfigResponse {
  /**
   * 模型
   */
  baseModel: EditorConfigBaseModelResponse[];
  /**
   * 推荐模式下的模型
   */
  recommendBaseModel: EditorConfigModelResponse[];
  /**
   * 风格模型
   */
  styleModel: EditorConfigModelResponse[];

  maatConf: EditorConfigMattResponse;
  /**
   * 标签
   */
  publishTags: EditorConfigPublishResponse[];
  /**
   * 采样器
   */
  samplerIndex: Array<{ label: string; value: string }>;
  /**
   * 预处理器分类列表
   */
  moduleList: EditorConfigModuleListResponse[];
  /**
   * 高级模型外层 tag
   */
  baseTagUrl: string;
  /**
   * 推荐模型外层 tag
   */
  recommendTagUrl: string;
  /**
   * 功能入口信息
   */
  functionEntry?: {
    content?: string;
    icon?: string;
    scheme?: string;
  };
}

export enum MODEL_TYPE {
  BASE = 1,
  HYPER_NETWORK = 4,
  LORA = 6,
  CONTROL_NET = 8,
}

export interface EditorConfigModelResponse {
  /**
   * 分类id
   */
  categoryId: number;
  /**
   * 分类名称
   */
  categoryName: string;

  /**
   * 详情列表
   */
  list: EditorConfigModelListResponse[];
}

export interface EditorConfigBaseModelResponse
  extends Omit<EditorConfigModelResponse, "list"> {
  list: EditorConfigBaseModelListResponse[];
}

export interface EditorConfigBaseModelListResponse
  extends EditorConfigModelListResponse {
  /**
   * 限定风格模型列表，该列有值则风格模型列表从这边选择
   */
  limitStyleModel?: EditorConfigModelResponse[];
}

export interface EditorConfigModelListTagResponse {
  /**
   * 标签id
   */
  id: number;
  /**
   * 标签名称
   **/
  name: string;
  /**
   * 标签图标URL
   **/
  url: string;
}

export interface EditorConfigControlNetModel {
  /**
   * 模型id
   */
  id: number;
  /**
   * 名称
   */
  name: string;
  /**
   * 预处理器
   */
  module: string;
  /**
   * 对应模型
   */
  model: string;
  /**
   * 简介
   */
  desc: string;
  /**
   * 封面图片url
   */
  coverPic: string;
  /**
   * 角标url
   */
  tagUrl?: string;
  /**
   * 提示的示例图片
   */
  sampleTips?: {
    /**
     * 参考图
     */
    samplePic?: string;
    /**
     * 预处理
     */
    prePic?: string;
    /**
     * 结果图
     */
    resultPic?: string;
  };
  /**
   * 预设的可供选择的图片
   */
  choosablePics?: string[];
  /**
   * 默认权重
   */
  weight: number;
  /**
   * 最大权重
   */
  weightMax: number;
  /**
   * 权重slider步长
   */
  weightStep: number;
  /**
   * 开始时段默认值
   */
  guidanceStart: number;
  /**
   * 开始时段最大值
   */
  guidanceStartMax: number;
  /**
   * 结束时段默认值
   */
  guidanceEnd: number;
  /**
   * 结束时段最大值
   */
  guidanceEndMax: 1;
  /**
   * 结束时段slider步长
   */
  guidanceStep: number;
  /**
   * 开始时段和结束时段的最小差值
   */
  guidanceInterval: number;
}

export interface EditorConfigModuleListResponse {
  /**
   * 分类id
   */
  categoryId: number;
  /**
   * 分类名称
   */
  categoryName: string;
  /**
   * 明细列表
   */
  list: EditorConfigControlNetModel[];
}

export type BaseModeSizeRatio = {
  /**
   * 宽度
   */
  width: number;
  /**
   * 高度
   */
  height: number;
  /**
   * 宽度/高度比
   */
  ratio: string;
};

// 基础模型绑定的的参数
export interface BaseModelDefaultParams {
  /**
   * 创意相关性 ( prompt相关性权重 CFG)
   */
  cfgScale: number;
  /**
   * 采样器
   */
  samplerIndex: string;
  /**
   * 模型采样步骤/采样迭代步数
   */
  steps: number;
  /**
   * 随机数种子，默认-1（开启随机就是传值-1）
   */
  seed: number;

  sizeRatio: BaseModeSizeRatio[]
}

export interface EditorConfigModelListResponse {
  /**
   * 模型id
   */
  id: number;
  /**
   * 一个业务用来hardcode的id
   */
  modelFlag: number;
  /**
   * 模型名称
   */
  name: string;
  /**
   * 类型枚举值：1.模型.HyperNetwork，6.LoRA，8.Controlnet，
   */
  type: MODEL_TYPE;

  /** 类型所对应的名称
   * 1.Checkpoint Trained; 2.Checkpoint Merge; 3.Textual Inversion; 4.HyperNetwork; 5.Aesthetic Gradient; 6.LoRA; 7.LyCORIS; 8.Controlnet; 9.Poses; 10.Wildcards; 11.Other;
   */
  typeName: string;

  /**
   * 模型描述
   */
  desc: string;
  /**
   * 模型封面，可能多个图片，默认使用第一个
   */
  images: string[] | string;
  /**
   * 模型的限制提示，例如:"当前模型暂不支持生成中文文案"
   */
  tips?: string;
  /**
   * 是否喜欢，喜欢的图标❤️/⭐️亮起，并且在收藏中
   */
  isCollect: boolean;

  /**
   * 后台配置的默认风格模型强度
   */
  styleModelWeight: number;

  /**
   * 标签
   */
  tag?: EditorConfigModelListTagResponse;

  defaultParams: BaseModelDefaultParams;
  /**
   * 大模型类型 0 非大模型 1 普通大模型 2 特殊限制大模型前端样式
   */
  mvType: number;
  /**
   * 限制采样器列表，该数据不为空时采样器列表取该列表数据
   */
  limitSamplerIndex: Array<{ label: string; value: string }>;
  pngImages?: string;
  pngName?: string;
}

export interface EditorConfigPublishResponse {
  /**
   * 标签id
   */
  tagId: 1 | 2 | 3 | 4 | 5;
  /**
   * 标签名称
   */
  tagName: TAG_NAME;
}

export enum TAG_NAME {
  SHOES = "球鞋",
  BIO = "仿生",
  CARTOON = "动漫",
  TWO_D_WORLD = "二次元",
  REAL = "真人",
}

export interface EditorConfigMattResponse {
  /**
   * 域名
   */
  host: string;
  /**
   * app名称
   */
  app: string;
  /**
   * 上传类型
   */
  type: "editor";
  /**
   * 数量
   */
  count: number;
  /**
   * 后缀
   */
  suffix: string;
  /**
   * 秘密钥+提交参数合成，用于服务端与客户端通信间加密验证
   */
  sig: string;
  /**
   * 时间变量
   */
  sigTime: string;
  /**
   * 加密服务版本号
   */
  sigVersion: string;
  /**
   * 大账号token，返回只是代表识别到了。这里返回的是下划线的【_】，使用时是用中间横线的【-】
   */
  accessToken: string;
}

/** 风格模型列表请求参数 */
export interface StyleModelListQuery {
  /** 请求来源  img2img图生图，txt2img文生图，aimodelimg-ai模特图 */
  from?: DraftType;
  /** 分类id   不传则返回全部分类第一页数据 */
  categoryId?: number;
  /** 搜索内容，必须带上对应的分类id */
  keyword?: string;
  /** 翻页用的标识 ，必须带上对应的分类id */
  cursor?: string;
  /** 自定义风格模型ids查询 */
  styleModelIds?: string;
  // 根据基础模型查询风格模型
  baseModelId?: number;
  /** 分页每一页的条数 */
  count?: number;
}

/** 风格模型列表返回参数 */
export interface StyleModelListResponse {
  /** 分类列表 */
  categoryList: (Omit<StyleModelResponse, "list"> & {
    /** 翻页用的标识 */
    cursor: string;

    list: (Omit<EditorConfigModelListResponse, "styleModelWeight"> & {
      weight: number;
    })[];
  })[];
  /** 按钮角标 */
  moreButtonLabel: string;
}

/** 风格模型返回参数 */
export interface StyleModelResponse extends EditorConfigModelResponse {
  cursor?: string;
  fetchingCursor?: string;
}

// 选中的风格模型
export interface ActiveStyleModel
  extends Pick<EditorConfigModelListResponse, "id" | "styleModelWeight"> {
  categoryIds: string;
  /**
   * 填空输入时的提示词
   */
  defaultPrompt: string;
  /**
   * 提示词中的黑名单
   */
  blackPrompt?: string;
}

export interface BaseModelCardProps {
  extra?: React.ReactNode;
  onClick?(): void;
  onExtraClick?(): void;
  children?: React.ReactNode;
  src?: string;
  desc?: string;
  tag?: string;
  title?: string;
  className?: string;
  // 角标背景图
  cornerLabelUrl?: string;
  showCornerLabelUrl?: boolean;
  /**
   *  自定义预览图
   */
  renderPreview?: (src?: string) => React.ReactElement;

  /**
   * 自定义描述
   */
  renderDesc?: (desc?: string) => React.ReactElement;
}

export interface ModelCardProps<T>
  extends Omit<BaseModelCardProps, "src" | "title" | "tag" | "desc"> {
  onChange?: (value: T) => void;
  value?: T;
  list: Required<
    Pick<
      BaseModelCardProps,
      "desc" | "src" | "tag" | "title" | "cornerLabelUrl"
    > & {
      id: number;
      baseModelDefaultParams: BaseModelDefaultParams;
      mvType: number;
    }
  >[];
  childrenFormName?: string;
}

export interface BaseModelInfo
  extends Pick<
    BaseModelCardProps,
    "desc" | "src" | "tag" | "title" | "cornerLabelUrl"
  > {
  id: number;
  baseModelDefaultParams: BaseModelDefaultParams;
  mvType: number;
}

// 基础模型类型
export enum ModelMvType {
  Not = 0,
  Normal = 1,
  Special = 2,
}
