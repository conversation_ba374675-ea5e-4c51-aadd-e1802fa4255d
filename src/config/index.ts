import { devConfig } from "./dev";
import { preConfig } from "./pre";
import { betaConfig } from "./beta";
import { prodConfig } from "./prod";

const baseConfig = {
  // 应用名称
  REACT_APP_TITLE: "whee-global",

  // # 静态资源路径
  PUBLIC_URL: "/",

  // # 谷歌第三方 id（账号使用）
  GOOGLE_CLIENT_ID:
    "*************-v89jrol3iikdkh1sllq4m45r4pkb4i6s.apps.googleusercontent.com",

  // 账号client id
  ACCOUNT_CLIENT_ID: "**********",

  // cookie 名称
  AUTH_TOKEN: "auth_token",

  // intercom app id
  INTERCOM_APP_ID: "b5kfg2e5",

  // 统计SDK moduleId
  STATISTIC_MODULE_ID: "whee_ai",

  // 订阅SDK appId
  SUBSCRIBE_APP_ID: "6829803307027000000",

  // 订阅中台 分组标识
  SUBSCRIBE_GROUP: "whee_group",

  // # 美豆商品分类标识
  REACT_APP_SUBSCRIBE_MEIDOU_CATEGORY: "whee_category_group_default_meidou",

  // # 会员商品分类标识
  REACT_APP_SUBSCRIBE_MEMBER_CATEGORY: "whee_category_group_default_subscribe",

  // 订阅中台基础会员ID
  SUBSCRIBE_BASIC_ID: "1030",

  // 订阅中台高级会员ID
  SUBSCRIBE_ADVANCED_ID: "2030",

  // 订阅美豆key
  SUBSCRIBE_MEIDOU_KEY: "wheeai.purchase.group1",

  /**
   * GTM 配置 默认dev - pre - beta 都是用此容器配置
   */
  GTM_ID: "GTM-PS7W2SVW",
  GTM_AUTH: "5Il2r-FN1rQtIdOB-Ohs-g",
  GTM_PREVIEW: "env-2",
  GA_MEASUREMENT_ID: "G-YCTS18GG7Y",
};
// 一个根据环境变量来返回对应配置的函数
export const getConfig = () => {
  const publicEnv = process.env.NEXT_PUBLIC_ENV;
  if (publicEnv === "dev") {
    return {
      ...baseConfig,
      ...devConfig,
    };
  }
  if (publicEnv === "pre") {
    return {
      ...baseConfig,
      ...preConfig,
    };
  }
  if (publicEnv === "beta") {
    return {
      ...baseConfig,
      ...betaConfig,
    };
  }
  return {
    ...baseConfig,
    ...prodConfig,
  };
};
