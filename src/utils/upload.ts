import { UploadEnv, Uploader, UploaderOptions, UploadOptions, UploadProgress, UploadTokenRequestParamsWithoutSign } from "@meitu/upload";
import { isBeta, isRelease } from '@/constants/environment';
import { isDebugMode } from "@meitu/util";
import { fetchImageUploadSign } from "@/api/image";

const defaultEnv = isRelease || isBeta ? UploadEnv.Release : UploadEnv.Test;

type CreateUploaderOptions = Pick<UploaderOptions, 'accessToken'>;

const getUploader = (() => {
  
  let uploader: null | Uploader = null;
  let optionsCache: CreateUploaderOptions | null = null;

  function createUploader( options: CreateUploaderOptions) {
    return new Uploader({
      env: defaultEnv,
      debug: isDebugMode(),
      async sign(params) {
        const { type, suffix } = params;
        const signedParams = await fetchImageUploadSign({ type, suffix }); // 请求业务服务端提供的参数签名接口
        return signedParams; // 返回签名后的参数，相当于 params + 3 个新增参数 { sig: 'xxx', sigTime: 'xxx', sigVersion: 'xxxx' }
      },
      ...options,
    });
  }

  function isEqualsOptions(options: CreateUploaderOptions) {
    if (!optionsCache) {
      return false;
    }

    if (options === optionsCache) {
      return true;
    }

    for(const [key, value] of Object.entries(options)) {
      if (Object.is(value, optionsCache?.[key as keyof CreateUploaderOptions])) {
        return false;
      }
    }

    return true;
  }

  return (options: CreateUploaderOptions) => {
    if (!optionsCache || !uploader || !isEqualsOptions(options)) {
      uploader = createUploader(options);
    }

    return uploader;
  }
  
})();


type UploadConfig = {
  signal?: AbortSignal;
  timeout?: number;
  uploadOptions?: UploadOptions;
}

export function upload(
  file: File | Blob | string | FileList | Blob[] | string[] | FileList[],
  uploaderOptions: CreateUploaderOptions,
  params: UploadTokenRequestParamsWithoutSign,
  config?: Partial<UploadConfig>
) {
  const uploader = getUploader(uploaderOptions);
  
  let taskId = null as null | string;
  const cancelUpload = () => {
    taskId && uploader.cancel(taskId)
  }


  const wrapperHandleProgress = (progress: UploadProgress) => {
    taskId = progress.taskId;
  }
  
  const uploadPromise = uploader.upload(file, params, {
    ...config?.uploadOptions,
    progress: wrapperHandleProgress,
  });

  config?.signal?.addEventListener('abort', cancelUpload, { once: true })


  if (config?.timeout) {
    setTimeout(cancelUpload, config.timeout)
  }

  return uploadPromise;
}