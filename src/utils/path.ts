/**
 * @params url
 * @description 处理url添加html后缀，区分path上是否有参数
 */
export function MTPath(url: string): string {
  // url 需去除?后面的字符或#后字符
  let qIndex = url.indexOf("?");
  let jIndex = url.indexOf("#");
  let qText = qIndex > 0 ? url.substring(qIndex) : "";
  let jText = jIndex > 0 ? url.substring(jIndex) : "";
  let path =
    qIndex > 0
      ? url.substring(0, qIndex)
      : jIndex > 0
      ? url.substring(0, jIndex)
      : url;

  return `${path}.html${qText ? qText : jText}`;
}

export const getEnv = () => {
  const host = window.location.host;
  let env = "";
  if (host.startsWith("pre-") || host.startsWith("localhost")) {
    env = "pre";
  } else if (host.startsWith("pre1-")) {
    env = "pre1";
  } else if (host.startsWith("pre2-")) {
    env = "pre2";
  } else if (host.startsWith("stable")) {
    env = "stable";
  }
  return env;
};
