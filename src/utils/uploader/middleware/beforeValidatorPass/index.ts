import { Middleware } from "koa-compose";
import mime from 'mime';
import { UploadContext } from "../..";
import { BeforeValidatorCheckList, BeforeValidatorOptions } from "./types";
import { createImage } from "@/utils/cropImage";
import { BeforeValidatorError } from "./error";

export function beforeValidatorPass(options: BeforeValidatorOptions): Middleware<UploadContext> {
  const sizeLimit = options.size;
  const minDimension = options.dimension?.min;
  const maxDimension = options.dimension?.max;
  const acceptType = new Set(options.extension?.map(ext => mime.getType(ext)).filter(Boolean) as Array<string> | undefined);

  return async function (context, next) {
    const { file, signal } = context;
  
    const checkList: BeforeValidatorCheckList = {
      size: true,
      ratio: {
        wh: true,
        hw: true,
      },
      dimension: {
        min: true,
        max: true,
      },
      extension: true,
    };

    let hasError = false;

    if (sizeLimit) {
      checkList.size = file.size <= sizeLimit * 1024 * 1024;
      hasError = hasError || !checkList.size;
    }

    if (!minDimension && !maxDimension) {
      return await next();
    }


    let width = 0;
    let height = 0;
    if (acceptType.size) {
      checkList.extension = acceptType.has(file.type);
      hasError = hasError || !checkList.extension;
      
      /**
       * 类型正确（图片）才会有判断宽和高
       */
      if (checkList.extension) {
        const url = URL.createObjectURL(file);
        try {
          const img = await createImage(url);
          width = img.naturalWidth;
          height = img.naturalHeight;
    
        } catch(e) {
          throw e;
        } finally {
          URL.revokeObjectURL(url);
        }
    
        signal?.throwIfAborted();
    
    
        if (maxDimension) {
          checkList.dimension.max = Math.max(width, height) <= maxDimension;
          hasError = hasError || !checkList.dimension.max;
        }
    
        if (minDimension) {
          checkList.dimension.min = Math.min(width, height) >= minDimension;
          hasError = hasError || !checkList.dimension.min;
        }

        if (options.ratio) {
          const wh = width / height;
          const hw = height / width;
          checkList.ratio.wh = wh <= options.ratio.wh;
          checkList.ratio.hw = hw <= options.ratio.hw;
          hasError = hasError || !checkList.ratio.wh || !checkList.ratio.hw;
        }
      }
    }

    if (hasError) {
      // console.log(options, checkList)
      throw new BeforeValidatorError(options, checkList, { type: file.type, size: file.size, dimension: [ width, height ] });
    }

    return await next();
  }
}