import {
  BeforeValidatorError,
  isBeforeValidatorError,
} from "@/utils/uploader/middleware/beforeValidatorPass/error";
import { isImageMonitorError } from "@/utils/uploader/middleware/monitorPass/error";
import toast from "@/components/Toast";
import { useI18n } from "@/locales/client";
import { isUploadPassError } from "../uploader/middleware/uploadPass/error";
export function uploadErrorhandler(e: Error, t: ReturnType<typeof useI18n>) {
  switch (true) {
    case isBeforeValidatorError(e): {
      const { checkList, options } = e as unknown as BeforeValidatorError;
      if (!checkList.extension) {
        return toast.error(t("Unsupported file format. Try a different one."));
      }
      if (!checkList.ratio.wh || !checkList.ratio.hw) {
        return toast.error("宽高比例超过3 请重新上传～");
      }
      if (!checkList.size) {
        return toast.error(
          t("Upload image size cannot exceed {size}M, please try again." as any, {
            size: options.size,
          })
        );
      }
      if (!checkList.dimension.max) {
        return toast.error(
          t("Upload image resolution cannot exceed {max}, please try again." as any, {
            max: options?.dimension?.max,
          })
        );
      }
      return;
    }

    case isImageMonitorError(e): {
      return toast.error(e.message ?? "");
    }


    case isUploadPassError(e): {
      return toast.error("上传失败请重试");
    }
    
    default: {
      return false;
    }
  }
}
