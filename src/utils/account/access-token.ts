import { getLocalStorageItem, removeLocalStorageItem, setLocalStorageItem } from "@meitu/util";

export const TOKEN_CACHE_KEY = 'aigc-editor:token';

export function getAccountAccessToken() {
  if (typeof window === "undefined") {
    return null;
  }

  return getLocalStorageItem(TOKEN_CACHE_KEY);
}

export function setAccountAccessToken(token: string) {
  if (typeof window === "undefined") {
    return;
  }

  if (!token) {
    clearAccountAccessToken();
    return;
  }

  setLocalStorageItem(TOKEN_CACHE_KEY, token);
}

export function clearAccountAccessToken() {
  if (typeof window === "undefined") {
    return;
  }

  removeLocalStorageItem(TOKEN_CACHE_KEY);
}