'use client'
import type { InternalAxiosRequestConfig, CreateAxiosDefaults } from "axios";
import axios, { AxiosInstance } from "axios";
import cloneDeep from 'lodash/cloneDeep'

// import { accountClientId, getTraceChannelParams } from '@/services';
import { toCamelCase, toSnakeDict } from "@meitu/util";
// import { createCommunityAxiosInstance } from '../createCommunityAxiosInstance';
import mtstat from "@meitu/mtstat-sdk";
import { toMTCCRequestHeaders } from "@/utils/toMTCCRequestHeaders";
// import { abTest } from '@/services';
import { v4 as uuid } from "uuid";
import { MtccFuncCode } from "@/types/common";
import { FunctionCode } from "@/api/types/meidou";
import { message } from "antd";
import { getAccountAccessToken } from "../rootStorePicker/user";
import { AIPoster } from "@/api/types/aiPoster/task";
import toast from "@/components/Toast";
import { transformPosterConfigStruct } from "./utils";

const WheeAppId = "2000018";

/** 业务响应码 */
enum BizCode {
  // 未登录
  Unauthorized = 10022,

  // 禁止访问
  Forbidden = 11004,

  // 权限不足
  NoPermission = 11020,
}

const accountParams = toSnakeDict({
  clientId: "**********",
});
// 业务需求，需要获取客户端时区
const getLocalTimeZone = () => {
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  return timeZone;
};
const clientTimezone = getLocalTimeZone();

// 获取当前浏览器的语言
const getBrowserLanguage = () => {
  // return navigator.language;
  return "en";
};

// TODO: 服务端执行时 这里的设备Id？
// 兼容服务端渲染 防止服务端执行时读取localStorage 导致页面500
const getDeviceId = () =>
  typeof window !== "undefined" ? mtstat.getDeviceId() : "";

export function shimRequestGeneralParams(
  config: InternalAxiosRequestConfig,
  extra: Record<string, any> = {}
) {
  if (!config.method) {
    return config;
  }
  const gnum = getDeviceId();

  switch (config.method.toUpperCase()) {
    case "GET": {
      config.params = {
        ...config.params,
        ...accountParams,
        gnum,
        client_timezone: clientTimezone,
        client_language: getBrowserLanguage(),
        ...extra,
      };
      break;
    }

    default: {
      config.data = {
        ...config.data,
        ...accountParams,
        client_timezone: clientTimezone,
        client_language: getBrowserLanguage(),
        gnum,
        ...extra,
      };
    }
  }

  return config;
}

/**
 * 傻逼的公共参数垫片
 * @param config 请求配置
 * @param extra 自定义额外的一个写公共参数
 */
export function shimWheeRequestGeneralParams(
  config: InternalAxiosRequestConfig,
  extra: Record<string, any> = {}
) {
  if (!config.method) {
    return config;
  }
  const gnum = getDeviceId();

  // 获取生成张数
  const getBatchSize = () => {
    if (config?.data) {
      // 有传了自定义张数参数，优先取值（目前只针对延伸创作）
      if (config.data.custom_batch_size) {
        return config.data.custom_batch_size;
      }
      if (config?.data?.params && typeof config?.data?.params === "string") {
        return (
          JSON.parse(config?.data?.params).batch_size ||
          JSON.parse(config?.data?.params).generate_num ||
          "1"
        );
      } else {
        return (
          config?.data?.params?.batch_size ||
          config?.data?.batch_size ||
          config?.data?.generate_num ||
          "1"
        );
      }
    }
  };

  switch (config.method.toUpperCase()) {
    case "GET": {
      config.params = {
        ...config.params,
        ...accountParams,
        gnum,
        client_language: getBrowserLanguage(),
        client_timezone: clientTimezone,
        ...extra,
        _t: Date.now(),
      };
      break;
    }

    default: {
      //  煞笔的公共参数垫片 post 需要用query 的形式处理
      config.params = {
        client_language: getBrowserLanguage(),
        client_timezone: clientTimezone,
      },
        config.data = {
          ...config.data,
          ...accountParams,
          client_timezone: clientTimezone,
          client_language: getBrowserLanguage(),
          gnum,
          ...extra,
        };
    }
  }

  const mtccData = toMTCCRequestHeaders(
    WheeAppId,
    "EN",
    // TODO
    // config?.data?.function_name === MtccFuncCode.FuncCodeModelTraining
    //   ? "ModelTraining"
    //   : "CreativeTools",
    "CreativeTools",
    {
      osType: "web",
      gnum: String(getDeviceId()),
      bizId: WheeAppId,
      orderId: uuid(),
      function: {
        name: AIPoster.TaskFunctionCode[config?.data?.task_category as keyof typeof AIPoster.TaskFunctionCode] || 'other',
      },
      mediaType: config?.data?.media_type,
      resMediaType: config?.data?.res_media_type,
      extInfo: {
        number: getBatchSize(),
      },
    }
  ) as any;
  // console.log)

  config.headers = {
    ...config.headers,
    ...mtccData,
  };

  return config;
}

export function createInstance(config?: CreateAxiosDefaults): AxiosInstance {
  //   const [channelParams] = getTraceChannelParams();
  const channelParams = {
    channel: "",
  };

  const instance = axios.create({
    ...config,
    baseURL: `/aigc/api${config?.baseURL}`,
  });

  instance.interceptors.request.use(async (config) => {
    // config.headers['X-App-Origin'] = appOrigin;
    // 判断是否是客户端，只有客户端才设置access-token 在header头里

    if(typeof window !== 'undefined') {
      config.headers['Access-Token'] = getAccountAccessToken();
    }
    // let abInfo = await abTest.getABInfo();
    // config.headers['ab_info'] = JSON.stringify(abInfo);

    // config.headers['Access-Token'] = getAccountAccessToken();

    if (channelParams && channelParams?.channel) {
      Object.assign(config.headers, channelParams);
    }

    return shimWheeRequestGeneralParams(config);
  });

  instance.interceptors.response.use(
    (response) => {
      const url = response.config.url
      const isPosterConfig = url === '/ai_poster_config.json'
      if (isPosterConfig) {
        let newConfig
        if (response?.data?.data?.template) {
          newConfig = cloneDeep(response.data.data.template)
        }
        const result = toCamelCase(response.data.data)
        if (newConfig) {
          result.template = transformPosterConfigStruct(newConfig)
        }
        return result
      }
      return toCamelCase(response.data.data)
    },
    (axiosError) => {
      const response = axiosError.response;
      const url = `${axiosError.config!.baseURL}${axiosError.config!.url}`;
      const status = response?.status;
      const code = response?.data?.code;

      if (status === 401 || (status === 400 && code === BizCode.Unauthorized)) {
        if (url === "/aigc/api/user/me.json") {
          throw undefined;
        }
        if (
          url === "/aigc/api/image_modify/project/save.json" ||
          url === "/aigc/api/image/get_sign.json"
        ) {
          throw axiosError;
        }

        // removeAccountAccessToken();
        // account.logout({ relogin: true });
        throw axiosError;
      }

      if (status === 403 || (status === 400 && code === BizCode.Forbidden)) {
        // window.location.href = process.env.REACT_APP_CREATION_GUIDE_LINK;
        throw axiosError;
      }

      // if(status === 400 && url.includes('prompt_optimize.json')) {
      //   toast.error(response?.data?.message);
      //   throw axiosError;
      // }

      if (code === BizCode.NoPermission) {
        toast.error(response?.data?.message);
        setTimeout(() => {
          //   window.location.href = process.env.REACT_APP_LINK;
        }, 300);
      }

      throw axiosError;
    }
  );

  return instance;
}

export function createMockInstance(pathname: string): AxiosInstance {
  return axios.create({
    baseURL: `https://api-mock.meitu-int.com/mock/2243${pathname}`,
  });
}
