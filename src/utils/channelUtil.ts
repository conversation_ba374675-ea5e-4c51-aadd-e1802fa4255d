import { parse } from "qs";
import sessionStorageUtil from "./sessionStorageUtil";

function getQueryMap() {
  const map: Record<string, string> = {};
  Object.assign(map, parse(window.location.search.split("?")[1] || ""));
  Object.assign(map, parse(window.location.hash.split("?")[1] || ""));
  return map;
}
export const initChannel = () => {
  const itemMap = getQueryMap();
  const channelKey = "channel";
  const firstUrlKey = "first_url";
  const matrixChannel = "matrix_channel";
  if (itemMap[matrixChannel]) {
    sessionStorageUtil.setItem(matrixChannel, itemMap[matrixChannel] || "");
  }
  if (itemMap[channelKey]) {
    sessionStorageUtil.setItem(channelKey, itemMap[channelKey] || "");
    sessionStorageUtil.setItem(
      firstUrlKey,
      itemMap[firstUrlKey] || window.location.href
    );
  }
};

export const getChannelKey = () => {
  const channelValue = sessionStorageUtil.getItem("channel") || "";
  if (channelValue) {
    return decodeURIComponent(channelValue);
  }
  initChannel();
  return decodeURIComponent(sessionStorageUtil.getItem("channel") || "");
};

export const getFirstUrl = () => {
  const firstUrlValue = sessionStorageUtil.getItem("first_url") || "";
  if (firstUrlValue) {
    return decodeURIComponent(firstUrlValue);
  }
  initChannel();
  return decodeURIComponent(sessionStorageUtil.getItem("first_url") || "");
};

export const getMatrixChannel = () => {
  const value = sessionStorageUtil.getItem("matrix_channel") || "";
  if (value) {
    return decodeURIComponent(value);
  }
  initChannel();
  return decodeURIComponent(sessionStorageUtil.getItem("matrix_channel") || "");
};
