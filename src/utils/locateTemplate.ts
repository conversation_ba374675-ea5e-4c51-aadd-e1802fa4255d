/**
 * 将右侧列表定位到指定的template
 * 
 * 将template放到对应列表的第一个位置
 */

import { TemplateItemType } from "@/api/types/poster"
import { ConfigStore } from "@/app/[locale]/ai/poster/_store/config";
import { EditorStatusStore, RightSiderTabsKey } from "@/app/[locale]/ai/poster/_store/editorStatus"


type LocateTemplateParams = {
  template: TemplateItemType,
  editorStatusStore: EditorStatusStore,
  editorConfigStore: ConfigStore,
}

export function locateTemplate({ template, editorStatusStore, editorConfigStore }: LocateTemplateParams) {
  if (!template.categoryId) {
    return;
  }

  editorStatusStore.setRightSiderActiveTab(RightSiderTabsKey.Template);
  editorConfigStore.setActiveCategoryId(template.categoryId);
  editorConfigStore.addAttachTemplates(template);
  editorStatusStore.templateScrollContainer?.scrollIntoView({
    behavior: 'smooth',
    block: 'start',
  });
  editorStatusStore.templateTabs.get(template.categoryId)?.scrollIntoView({
    behavior: 'smooth',
    inline: 'center',
  });
}